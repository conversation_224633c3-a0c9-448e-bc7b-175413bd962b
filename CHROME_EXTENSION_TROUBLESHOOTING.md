# 🔧 **Chrome Extension Troubleshooting Guide - Islamic Azkar**

## 🚨 **Issue Resolved: Manifest File Error**

**Problem:** "ملف البيان مفقود أو غير قابل للقراءة" (Manifest file is missing or unreadable)

**Root Cause:** Incorrect icon file name (`icon128.png.png` instead of `icon128.png`)

**Status:** ✅ **FIXED** - Icon file renamed correctly

---

## 📋 **Pre-Installation Checklist**

### **1. Verify File Structure**
Your extension folder should contain exactly these files:

```
اضافت جوجل كروم/
├── manifest.json                 ✅ Present
├── background.js                 ✅ Present  
├── popup.html                    ✅ Present
├── popup.js                      ✅ Present
├── styles.css                    ✅ Present
├── data/
│   └── azkar.js                  ✅ Present
├── images/
│   ├── icon16.png                ✅ Present
│   ├── icon48.png                ✅ Present
│   └── icon128.png               ✅ Fixed (was icon128.png.png)
└── [Documentation files]        ✅ Present
```

### **2. File Validation Checklist**

**Run these checks before loading the extension:**

#### **A. Manifest.json Validation:**
- ✅ File exists in root directory
- ✅ Valid JSON syntax (no trailing commas)
- ✅ Manifest version 3 specified
- ✅ All referenced files exist

#### **B. Icon Files Validation:**
- ✅ `images/icon16.png` exists
- ✅ `images/icon48.png` exists  
- ✅ `images/icon128.png` exists (FIXED)
- ✅ No double file extensions

#### **C. Core Files Validation:**
- ✅ `popup.html` exists and valid
- ✅ `background.js` exists and valid
- ✅ `popup.js` exists and valid
- ✅ `styles.css` exists and valid
- ✅ `data/azkar.js` exists and valid

---

## 🔧 **Step-by-Step Installation Instructions**

### **Step 1: Open Chrome Extensions Page**
1. Open Google Chrome
2. Type in address bar: `chrome://extensions/`
3. Press Enter

### **Step 2: Enable Developer Mode**
1. Look for "Developer mode" toggle in top-right corner
2. Click to enable it (should turn blue/green)

### **Step 3: Load the Extension**
1. Click "Load unpacked" button
2. Navigate to your extension folder: `~\OneDrive\سطح المكتب\اضافت جوجل كروم`
3. Select the folder (not individual files)
4. Click "Select Folder"

### **Step 4: Verify Installation**
- ✅ Extension appears in the list
- ✅ No error messages shown
- ✅ Extension icon appears in Chrome toolbar
- ✅ Toggle switch is ON (enabled)

---

## 🚨 **Common Error Solutions**

### **Error 1: "Manifest file is missing or unreadable"**
**Causes & Solutions:**
- ❌ **Wrong folder selected** → Select the folder containing manifest.json
- ❌ **File encoding issues** → Ensure manifest.json is UTF-8 encoded
- ❌ **Invalid JSON syntax** → Check for syntax errors
- ❌ **Missing referenced files** → Verify all files exist

### **Error 2: "Failed to load extension icon"**
**Causes & Solutions:**
- ❌ **Icon files missing** → Check images/ folder
- ❌ **Wrong file names** → Ensure exact names: icon16.png, icon48.png, icon128.png
- ❌ **Double extensions** → Remove .png.png, keep only .png

### **Error 3: "Service worker registration failed"**
**Causes & Solutions:**
- ❌ **background.js missing** → Verify file exists
- ❌ **JavaScript syntax errors** → Check console for errors
- ❌ **Invalid permissions** → Verify manifest permissions

### **Error 4: "Package is invalid"**
**Causes & Solutions:**
- ❌ **Invalid manifest structure** → Validate JSON syntax
- ❌ **Missing required fields** → Check manifest_version, name, version
- ❌ **Unsupported manifest version** → Use version 3

---

## 🧪 **Testing Your Extension**

### **Basic Functionality Test:**
1. **Click extension icon** → Popup should open
2. **Check tabs** → All tabs should be clickable
3. **Test settings** → Try changing times and saving
4. **Test notifications** → Set a time 1 minute ahead

### **Advanced Testing:**
1. **Open Developer Tools** (F12)
2. **Check Console** → No error messages
3. **Test all features** → Counters, tabs, settings
4. **Reload extension** → Should work after reload

---

## 🔍 **Debugging Tips**

### **If Extension Still Won't Load:**

1. **Check File Permissions:**
   - Ensure you have read access to all files
   - Try copying folder to a different location

2. **Validate JSON:**
   - Use online JSON validator for manifest.json
   - Check for hidden characters or encoding issues

3. **Check Chrome Version:**
   - Ensure Chrome is updated (version 88+)
   - Manifest V3 requires recent Chrome versions

4. **Clear Chrome Cache:**
   - Go to `chrome://settings/clearBrowserData`
   - Clear cached images and files

### **Console Debugging:**
1. Right-click extension icon → "Inspect popup"
2. Check Console tab for JavaScript errors
3. Check Network tab for failed file loads

---

## ✅ **Success Indicators**

**Your extension is working correctly if:**
- ✅ No error messages in chrome://extensions/
- ✅ Extension icon appears in toolbar
- ✅ Popup opens when clicking icon
- ✅ All tabs are functional
- ✅ Settings can be saved
- ✅ No console errors

---

## 📞 **Still Having Issues?**

**If problems persist:**
1. **Restart Chrome completely**
2. **Try loading in Incognito mode**
3. **Check Windows file permissions**
4. **Verify antivirus isn't blocking files**
5. **Try copying extension to C:\ drive**

---

## 🎉 **Extension Ready!**

Once successfully loaded, your Islamic Azkar extension will:
- ✅ Show prayer time notifications
- ✅ Display morning and evening remembrances
- ✅ Provide interactive counters
- ✅ Allow custom time settings
- ✅ Work offline completely

**May Allah bless this work and make it beneficial for Muslims worldwide** 🤲

---

*Last Updated: December 2024*
