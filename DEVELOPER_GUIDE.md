# دليل المطور - إضافة أذكار المسلم

## هيكل المشروع

```
اضافت جوجل كروم/
├── manifest.json          # ملف التكوين الرئيسي
├── background.js          # خدمة العمل في الخلفية
├── popup.html            # واجهة المستخدم الرئيسية
├── popup.js              # منطق واجهة المستخدم
├── styles.css            # تنسيق واجهة المستخدم
├── data/
│   └── azkar.js          # قاعدة بيانات الأذكار
├── images/
│   ├── icon16.png        # أيقونة 16×16
│   ├── icon48.png        # أيقونة 48×48
│   └── icon128.png       # أيقونة 128×128
├── test-extension.html   # ملف اختبار الإضافة
├── README.md             # دليل المستخدم
├── DEVELOPER_GUIDE.md    # دليل المطور
└── تعليمات_التثبيت.txt   # تعليمات سريعة
```

## الملفات الأساسية

### 1. manifest.json
- **الغرض:** تكوين الإضافة وتحديد الصلاحيات
- **المعايير:** Chrome Extension Manifest V3
- **الصلاحيات:**
  - `storage`: لحفظ إعدادات المستخدم
  - `alarms`: لجدولة الإشعارات
  - `notifications`: لعرض التنبيهات

### 2. background.js
- **الغرض:** إدارة المنبهات والإشعارات
- **الوظائف الرئيسية:**
  - `setupAlarms()`: إعداد منبهات الأذكار
  - `showNotification()`: عرض الإشعارات
  - معالجة أحداث النقر على الإشعارات

### 3. popup.html/js
- **الغرض:** واجهة المستخدم التفاعلية
- **المكونات:**
  - نظام التبويبات
  - عرض الأذكار
  - العداد التفاعلي
  - إعدادات الأوقات

### 4. data/azkar.js
- **الغرض:** قاعدة بيانات الأذكار
- **المحتوى:**
  - أذكار الصباح (`morningAzkar`)
  - أذكار المساء (`eveningAzkar`)
  - أذكار بعد الصلاة (`afterPrayerAzkar`)

## المميزات التقنية

### 1. إدارة الوقت والمنبهات
```javascript
// إعداد منبه يومي
chrome.alarms.create('morningAzkar', {
  when: morningTime.getTime(),
  periodInMinutes: 24 * 60
});
```

### 2. تخزين الإعدادات محلياً
```javascript
// حفظ الإعدادات
chrome.storage.local.set({ azkarTimes: settings });

// قراءة الإعدادات
chrome.storage.local.get(['azkarTimes'], callback);
```

### 3. العداد التفاعلي
- عداد لكل ذكر حسب عدد المرات المطلوب
- تغيير اللون عند الإكمال
- إمكانية إعادة التعيين

### 4. معالجة الأخطاء
- التحقق من وجود العناصر قبل الوصول إليها
- معالجة أخطاء Chrome API
- رسائل خطأ واضحة للمستخدم

## إرشادات التطوير

### 1. إضافة أذكار جديدة
```javascript
// في ملف data/azkar.js
const newAzkar = [
  {
    text: "نص الذكر",
    translation: "الترجمة أو التفسير (اختياري)",
    count: 3 // عدد المرات
  }
];
```

### 2. تخصيص الأوقات
```javascript
// في background.js
const DEFAULT_TIMES = {
  morning: "05:00",
  evening: "17:00",
  afterPrayer: true
};
```

### 3. تعديل التصميم
- استخدم ملف `styles.css` لتخصيص المظهر
- الإضافة تدعم RTL للغة العربية
- تصميم متجاوب للشاشات المختلفة

## اختبار الإضافة

### 1. اختبار محلي
```bash
# افتح Chrome وانتقل إلى
chrome://extensions/

# فعّل وضع المطور
# انقر "Load unpacked"
# اختر مجلد الإضافة
```

### 2. اختبار الوظائف
- استخدم ملف `test-extension.html`
- تحقق من وحدة التحكم للأخطاء
- اختبر جميع التبويبات والوظائف

### 3. اختبار الإشعارات
```javascript
// اضبط وقت قريب للاختبار
const testTime = new Date();
testTime.setMinutes(testTime.getMinutes() + 1);
```

## الأمان والخصوصية

### 1. البيانات المحلية
- جميع البيانات تُحفظ محلياً في المتصفح
- لا يتم إرسال أي بيانات لخوادم خارجية
- الأذكار مأخوذة من مصادر إسلامية موثوقة

### 2. الصلاحيات
- الإضافة تطلب الحد الأدنى من الصلاحيات
- لا تصل إلى بيانات المواقع الأخرى
- تعمل بشكل مستقل تماماً

## التحديثات المستقبلية

### مميزات مقترحة:
1. **إضافة أصوات التلاوة**
   - تشغيل تلاوة الأذكار صوتياً
   - خيارات قراء متعددين

2. **تذكيرات إضافية**
   - أذكار النوم والاستيقاظ
   - أذكار دخول المنزل والخروج

3. **إحصائيات**
   - تتبع عدد مرات قراءة الأذكار
   - إحصائيات يومية وأسبوعية

4. **تخصيص أكثر**
   - اختيار خطوط مختلفة
   - ألوان وثيمات متنوعة

## المساهمة في التطوير

### 1. إضافة مميزات جديدة
- اتبع نفس هيكل الكود الموجود
- أضف معالجة مناسبة للأخطاء
- اختبر المميزة بشكل شامل

### 2. إصلاح الأخطاء
- حدد المشكلة بدقة
- اختبر الحل في بيئات مختلفة
- وثّق التغييرات

### 3. تحسين الأداء
- قلل من استخدام الذاكرة
- حسّن سرعة التحميل
- اجعل الكود أكثر كفاءة

---

**للمطورين:** هذا المشروع مفتوح المصدر ويهدف لخدمة المسلمين. نرحب بمساهماتكم لتطويره وتحسينه.

**بارك الله فيكم** 🤲
