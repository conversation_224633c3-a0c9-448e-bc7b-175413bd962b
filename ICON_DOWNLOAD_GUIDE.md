# 🎨 دليل تحميل الأيقونات الإسلامية

## 🥇 الحل الأفضل: Iconfinder (مجاني تماماً)

### أيقونة المسجد الجميلة (الأكثر توصية):
**الرابط:** https://www.iconfinder.com/icons/4357697/masjid_muslim_pray_islam_religion_icon

**خطوات التحميل:**
1. انقر على الرابط أعلاه
2. انقر على زر "Download PNG" 
3. اختر الأحجام التالية:
   - **16px** → احفظها باسم `icon16.png`
   - **48px** → احفظها باسم `icon48.png`
   - **128px** → احفظها باسم `icon128.png`
4. ضع الملفات الثلاثة في مجلد `images/`

**المميزات:**
- ✅ مجانية تماماً (Creative Commons)
- ✅ عالية الجودة
- ✅ تصميم جميل ومناسب للأذكار
- ✅ لا تحتاج تسجيل

---

## 🥈 بدائل أخرى ممتازة:

### 1. أيقونة السبحة الإسلامية:
**الرابط:** https://www.iconfinder.com/icons/11334173/prayer_praying_prayer_beads_misbaha_tasbih_islamic_icon
- مناسبة جداً لتطبيق الأذكار
- تصميم أنيق وواضح

### 2. أيقونة الدعاء (يدين مرفوعتين):
**الرابط:** https://www.iconfinder.com/icons/3213234/drawn_hand_islam_muslim_praying_ramadan_icon
- تعبر عن الدعاء والذكر
- تصميم بسيط وجميل

### 3. من موقع Flaticon:
**الرابط:** https://www.flaticon.com/free-icons/islamic
- مجموعة كبيرة من الأيقونات الإسلامية
- تحتاج تسجيل مجاني

---

## 📁 ترتيب الملفات النهائي:

بعد تحميل الأيقونات، يجب أن يكون لديك:

```
اضافت جوجل كروم/
├── images/
│   ├── icon16.png    ← أيقونة 16×16 بكسل
│   ├── icon48.png    ← أيقونة 48×48 بكسل
│   └── icon128.png   ← أيقونة 128×128 بكسل
├── manifest.json
├── background.js
├── popup.html
├── popup.js
├── styles.css
└── data/
    └── azkar.js
```

---

## 🎯 نصائح مهمة:

1. **تأكد من الأسماء:** يجب أن تكون أسماء الملفات مطابقة تماماً:
   - `icon16.png`
   - `icon48.png`
   - `icon128.png`

2. **تأكد من المجلد:** ضع الأيقونات في مجلد `images/` فقط

3. **تأكد من الصيغة:** استخدم صيغة PNG فقط

4. **اختبر الأيقونات:** بعد التحميل، تأكد من ظهور الأيقونات في Chrome

---

## 🚀 بعد تحميل الأيقونات:

1. اتبع تعليمات التثبيت في ملف `README.md`
2. استخدم ملف `test-extension.html` لاختبار الإضافة
3. استمتع بالأذكار الإسلامية! 🤲

---

**بارك الله فيكم وجعل هذا العمل في ميزان حسناتكم**
