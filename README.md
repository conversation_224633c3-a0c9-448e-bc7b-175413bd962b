# إضافة أذكار المسلم لمتصفح Chrome

إضافة Chrome تعرض أذكار الصباح والمساء وأذكار بعد الصلاة في أوقات محددة مع إشعارات تلقائية.

## المميزات

- ✅ أذكار الصباح والمساء مع الأوقات المحددة
- ✅ أذكار بعد الصلاة
- ✅ إشعارات تلقائية في الأوقات المحددة
- ✅ عداد تفاعلي لتتبع عدد مرات قراءة كل ذكر
- ✅ واجهة باللغة العربية مع تصميم جميل
- ✅ إمكانية تخصيص أوقات الأذكار
- ✅ حفظ الإعدادات محلياً

## متطلبات التشغيل

- متصفح Google Chrome الإصدار 88 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## تعليمات التثبيت

### الخطوة 1: تحضير الملفات

1. تأكد من وجود جميع الملفات التالية في المجلد:
   ```
   اضافت جوجل كروم/
   ├── manifest.json
   ├── background.js
   ├── popup.html
   ├── popup.js
   ├── styles.css
   ├── data/
   │   └── azkar.js
   ├── images/
   │   ├── icon16.png
   │   ├── icon48.png
   │   └── icon128.png
   └── README.md
   ```

### الخطوة 2: تحميل الأيقونات الإسلامية

قبل تثبيت الإضافة، يجب تحميل الأيقونات المناسبة:

#### أ) من موقع Flaticon (الأفضل):

1. **اذهب إلى الرابط:** https://www.flaticon.com/free-icons/mosque

2. **أيقونات مقترحة:**
   - **أيقونة المسجد:** https://www.flaticon.com/free-icon/mosque_4840046
   - **أيقونة الهلال والنجمة:** https://www.flaticon.com/free-icon/islam_4250504
   - **أيقونة القرآن:** https://www.flaticon.com/free-icon/quran_4840048

3. **خطوات التحميل:**
   - انقر على الأيقونة المختارة
   - انقر "Download PNG"
   - اختر الأحجام التالية:
     * 16px → احفظها باسم `icon16.png`
     * 48px → احفظها باسم `icon48.png`
     * 128px → احفظها باسم `icon128.png`

#### ب) من موقع Icons8 (بديل):

1. **اذهب إلى:** https://icons8.com/icon/2078/moon-star

2. **حمّل أيقونة الهلال والنجمة:**
   - انقر "Download"
   - اختر تنسيق PNG
   - حمّل بالأحجام: 16×16، 48×48، 128×128

#### ج) وضع الأيقونات:
- ضع الأيقونات الثلاث في مجلد `images/`
- تأكد من الأسماء الصحيحة: `icon16.png`, `icon48.png`, `icon128.png`

### الخطوة 3: تثبيت الإضافة في Chrome

1. **افتح متصفح Chrome**

2. **اذهب إلى صفحة الإضافات:**
   - اكتب في شريط العنوان: `chrome://extensions/`
   - أو اذهب إلى القائمة ← المزيد من الأدوات ← الإضافات

3. **فعّل وضع المطور:**
   - في أعلى يمين الصفحة، فعّل خيار "وضع المطور" (Developer mode)

4. **حمّل الإضافة:**
   - انقر على زر "تحميل إضافة غير مُعبأة" (Load unpacked)
   - اختر مجلد الإضافة الذي يحتوي على ملف `manifest.json`
   - انقر "اختيار مجلد" أو "Select Folder"

5. **تأكيد التثبيت:**
   - ستظهر الإضافة في قائمة الإضافات المثبتة
   - تأكد من أن الإضافة مفعلة (مفتاح التشغيل في وضع ON)

### الخطوة 4: إعداد الإضافة

1. **انقر على أيقونة الإضافة:**
   - ستجد أيقونة الإضافة في شريط الأدوات
   - انقر عليها لفتح النافذة المنبثقة

2. **اذهب إلى تبويب الإعدادات:**
   - انقر على تبويب "الإعدادات"
   - حدد أوقات أذكار الصباح والمساء حسب تفضيلك
   - فعّل أو ألغِ تفعيل أذكار بعد الصلاة
   - انقر "حفظ الإعدادات"

3. **اختبار الإشعارات:**
   - تأكد من السماح للمتصفح بإرسال الإشعارات
   - ستحصل على إشعار عند حلول وقت الأذكار

## كيفية الاستخدام

### عرض الأذكار:
- انقر على أيقونة الإضافة في شريط الأدوات
- اختر التبويب المناسب (أذكار الصباح، المساء، أو بعد الصلاة)
- اقرأ الأذكار واستخدم العداد لتتبع عدد المرات

### استخدام العداد:
- انقر على زر "+" لزيادة العداد
- انقر على "إعادة تعيين" لبدء العد من جديد
- سيتغير لون العداد إلى الأخضر عند إكمال العدد المطلوب

### تخصيص الأوقات:
- اذهب إلى تبويب "الإعدادات"
- غيّر أوقات أذكار الصباح والمساء حسب رغبتك
- احفظ الإعدادات

## اختبار الإضافة

### اختبار سريع:
1. **افتح ملف الاختبار:** `test-extension.html` في المتصفح
2. **اتبع خطوات الاختبار** المذكورة في الملف
3. **تأكد من نجاح جميع الاختبارات** قبل الاستخدام

### اختبار الوظائف الأساسية:

#### 1. اختبار عرض الأذكار:
- ✅ انقر على أيقونة الإضافة
- ✅ تصفح جميع تبويبات الأذكار
- ✅ تحقق من ظهور النصوص بشكل صحيح

#### 2. اختبار العداد:
- ✅ جرب النقر على زر "+" في الأذكار
- ✅ تحقق من تغير الأرقام
- ✅ جرب زر "إعادة تعيين"

#### 3. اختبار الإعدادات:
- ✅ غيّر أوقات الأذكار
- ✅ احفظ الإعدادات
- ✅ أعد فتح الإضافة وتحقق من الحفظ

#### 4. اختبار الإشعارات:
- ✅ اضبط وقت قريب للاختبار
- ✅ انتظر ظهور الإشعار
- ✅ انقر على الإشعار

## استكشاف الأخطاء وإصلاحها

### مشاكل التثبيت:

#### إذا لم تظهر الإضافة:
- ✔️ تأكد من وجود جميع الملفات المطلوبة
- ✔️ تحقق من صحة ملف `manifest.json`
- ✔️ تأكد من وضع الأيقونات في مجلد `images/`
- ✔️ تحقق من تفعيل "وضع المطور" في Chrome

#### إذا ظهرت أخطاء في التحميل:
- ✔️ افتح أدوات المطور (F12) وتحقق من الأخطاء
- ✔️ تأكد من صحة أسماء الملفات
- ✔️ تحقق من وجود ملف `data/azkar.js`

### مشاكل الوظائف:

#### إذا لم تعمل الإشعارات:
- ✔️ تأكد من السماح للمتصفح بإرسال الإشعارات
- ✔️ اذهب إلى: `chrome://settings/content/notifications`
- ✔️ تأكد من أن الإشعارات مسموحة لجميع المواقع
- ✔️ جرب إعادة تشغيل المتصفح

#### إذا لم تحفظ الإعدادات:
- ✔️ تحقق من أن الإضافة لديها صلاحية "storage"
- ✔️ جرب إعادة تحميل الإضافة من صفحة الإضافات
- ✔️ تأكد من عدم وجود أخطاء في وحدة التحكم

#### إذا لم تظهر الأذكار:
- ✔️ تحقق من وجود ملف `data/azkar.js`
- ✔️ تأكد من تحميل الملف بشكل صحيح
- ✔️ افتح وحدة التحكم وتحقق من الأخطاء

## الدعم والمساعدة

إذا واجهت أي مشاكل في التثبيت أو الاستخدام، تأكد من:
- استخدام أحدث إصدار من Chrome
- وجود جميع الملفات في المكان الصحيح
- تفعيل وضع المطور في صفحة الإضافات

## الترخيص

هذه الإضافة مجانية للاستخدام الشخصي والتعليمي.
الأذكار مأخوذة من مصادر إسلامية موثوقة.

---

**بارك الله فيكم وجعل هذا العمل في ميزان حسناتكم** 🤲
