# تعليمات الاختبار الشامل لإضافة أذكار المسلم

## المشاكل التي تم إصلاحها

### 1. Content Security Policy (CSP) Violations ✅
- **المشكلة**: استخدام inline event handlers (onclick, onload, etc.)
- **الحل**: 
  - إضافة CSP policy في manifest.json
  - استبدال جميع onclick handlers بـ addEventListener
  - استبدال onload handlers بـ addEventListener

### 2. SVGElement className Error ✅
- **المشكلة**: استخدام className مع SVG elements
- **الحل**: استبدال svg.className بـ svg.setAttribute('class', ...)

## الملفات المُحدثة

1. **manifest.json**
   - إضافة content_security_policy

2. **popup.js**
   - إصلاح جميع onclick handlers
   - إصلاح onload handlers
   - إضافة event listeners بدلاً من inline handlers

3. **google-auth-manager.js**
   - إصلاح onclick handlers في HTML المولد ديناميكياً
   - إضافة event listeners مع setTimeout للعناصر المولدة

4. **visual-effects-manager.js**
   - إصلاح svg.className إلى svg.setAttribute('class', ...)

5. **detailed-statistics-manager.js**
   - إصلاح svg.className إلى svg.setAttribute('class', ...)

## كيفية إجراء الاختبار

### 1. الاختبار التلقائي
```javascript
// في console المتصفح، شغل:
window.comprehensiveTest.runAllTests();
```

### 2. الاختبار اليدوي

#### أ. فحص CSP Violations
1. افتح Developer Tools (F12)
2. انتقل إلى Console tab
3. ابحث عن أخطاء تحتوي على "Content Security Policy"
4. يجب ألا تجد أي أخطاء CSP

#### ب. اختبار Google Auth
1. انتقل إلى تبويب "الإعدادات"
2. اضغط على "تسجيل الدخول بحساب Google"
3. تحقق من عمل fallback mode إذا فشل التسجيل
4. تحقق من عدم وجود أخطاء في console

#### ج. اختبار عداد الأذكار
1. انتقل إلى "أذكار الصباح" أو "أذكار المساء"
2. اضغط على زر "تم القراءة" للأذكار المتكررة
3. تحقق من عمل العداد بشكل صحيح
4. اضغط على زر "إعادة تعيين" وتحقق من إعادة تعيين العداد

#### د. اختبار تشغيل الصوت
1. اضغط على أزرار التشغيل الصوتي (🔊)
2. تحقق من عدم وجود أخطاء في console
3. اختبر أزرار الإيقاف (⏹️)

#### هـ. اختبار تبديل الثيمات
1. انتقل إلى "الإعدادات"
2. جرب تبديل الثيمات المختلفة
3. تحقق من تطبيق الثيم بشكل صحيح

#### و. اختبار المشاركة
1. اضغط على أزرار المشاركة (📤)
2. تحقق من عمل نافذة المشاركة
3. تحقق من عدم وجود أخطاء

### 3. اختبار الأداء

#### أ. فحص الذاكرة
1. افتح Performance tab في Developer Tools
2. سجل لمدة 30 ثانية أثناء استخدام الإضافة
3. تحقق من عدم وجود memory leaks

#### ب. فحص الشبكة
1. افتح Network tab
2. تحقق من تحميل الملفات بشكل صحيح
3. تحقق من عدم وجود 404 errors

### 4. اختبار التوافق

#### أ. اختبار المتصفحات
- Chrome (الأساسي)
- Edge
- Firefox (إذا كان مدعوماً)

#### ب. اختبار الأحجام
1. اختبر على شاشات مختلفة الأحجام
2. اختبر في وضع landscape و portrait
3. تحقق من responsive design

## النتائج المتوقعة

### ✅ نجح الاختبار إذا:
- لا توجد أخطاء CSP في console
- لا توجد أخطاء JavaScript في console
- جميع الأزرار تعمل بشكل صحيح
- العدادات تعمل بشكل صحيح
- الثيمات تتبدل بشكل صحيح
- الصوت يعمل (إذا كان متاحاً)
- المشاركة تعمل
- البيانات تُحفظ وتُستعاد بشكل صحيح

### ❌ فشل الاختبار إذا:
- وجود أخطاء CSP
- وجود أخطاء JavaScript غير معالجة
- عدم عمل الأزرار
- مشاكل في العدادات
- مشاكل في تبديل الثيمات
- مشاكل في حفظ البيانات

## إصلاح المشاكل الإضافية

### إذا وجدت أخطاء CSP:
1. ابحث عن inline event handlers في HTML
2. استبدلها بـ addEventListener في JavaScript
3. تأكد من عدم استخدام eval() أو Function()

### إذا وجدت مشاكل SVG:
1. ابحث عن svg.className
2. استبدلها بـ svg.setAttribute('class', ...)
3. استخدم classList.add/remove للتلاعب بالفئات

### إذا وجدت مشاكل في الأداء:
1. تحقق من memory leaks
2. تحقق من event listeners غير المحذوفة
3. تحقق من timers غير المحذوفة

## ملاحظات مهمة

1. **الاختبار في بيئة الإنتاج**: تأكد من اختبار الإضافة بعد تحميلها في Chrome
2. **فحص console دائماً**: راقب console للأخطاء أثناء الاستخدام
3. **اختبار جميع الميزات**: لا تكتفي بالاختبار السطحي
4. **اختبار الحالات الحدية**: جرب سيناريوهات غير عادية
5. **اختبار الأداء**: راقب استهلاك الذاكرة والمعالج

## الدعم والمساعدة

إذا واجهت مشاكل:
1. تحقق من console للأخطاء
2. تحقق من Network tab للمشاكل في التحميل
3. تحقق من Application tab لمشاكل Storage
4. استخدم ملف comprehensive-test.js للاختبار التلقائي
