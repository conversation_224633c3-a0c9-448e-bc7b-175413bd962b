# 🎵 دليل الملفات الصوتية للأذكار

## 📁 هيكل مجلد الصوتيات

```
audio/
├── README_AUDIO.md          ← هذا الملف
├── morning/                 ← أذكار الصباح
│   ├── ayat-kursi.mp3      ← آية الكرسي
│   ├── ikhlas.mp3          ← سورة الإخلاص
│   ├── falaq.mp3           ← سورة الفلق
│   ├── nas.mp3             ← سورة الناس
│   └── [باقي أذكار الصباح]
├── evening/                 ← أذكار المساء
│   ├── ayat-kursi.mp3      ← آية الكرسي
│   ├── ikhlas.mp3          ← سورة الإخلاص
│   └── [باقي أذكار المساء]
├── prayer/                  ← أذكار بعد الصلاة
│   ├── tasbih.mp3          ← سبحان الله
│   ├── tahmid.mp3          ← الحمد لله
│   ├── takbir.mp3          ← الله أكبر
│   └── [باقي أذكار بعد الصلاة]
└── notification/            ← أصوات الإشعارات
    ├── morning-call.mp3     ← نداء أذكار الصباح
    ├── evening-call.mp3     ← نداء أذكار المساء
    └── prayer-call.mp3      ← نداء أذكار بعد الصلاة
```

## 🎙️ مصادر الملفات الصوتية المقترحة

### **1. مواقع التلاوات المجانية:**
- **موقع القرآن الكريم:** https://quran.com
- **موقع الشبكة الإسلامية:** https://islamweb.net
- **موقع طريق الإسلام:** https://ar.islamway.net

### **2. قراء مقترحون:**
- **الشيخ مشاري العفاسي**
- **الشيخ ماهر المعيقلي**
- **الشيخ سعد الغامدي**
- **الشيخ عبد الباسط عبد الصمد**

### **3. مواصفات الملفات الصوتية:**
- **التنسيق:** MP3
- **الجودة:** 128 kbps (توازن بين الجودة والحجم)
- **الحجم الأقصى:** 5 MB لكل ملف
- **المدة:** 30 ثانية - 3 دقائق حسب الذكر

## 📝 ملاحظات مهمة

### **حقوق الطبع والنشر:**
- تأكد من الحصول على إذن لاستخدام التلاوات
- استخدم تلاوات مجانية أو مفتوحة المصدر
- اذكر مصدر التلاوة في ملف credits.txt

### **التحسين للإضافة:**
- اضغط الملفات لتقليل الحجم
- استخدم أسماء ملفات باللغة الإنجليزية
- تأكد من وضوح الصوت وجودته

## 🔧 كيفية إضافة الملفات الصوتية

1. **حمّل الملفات الصوتية** من المصادر المقترحة
2. **ضعها في المجلدات المناسبة** حسب نوع الذكر
3. **تأكد من تطابق أسماء الملفات** مع الكود
4. **اختبر تشغيل الصوت** في الإضافة

---

**ملاحظة:** هذا المجلد جاهز لاستقبال الملفات الصوتية. الكود البرمجي سيبحث عن الملفات في هذه المجلدات تلقائياً.
