// مدير النسخ الاحتياطي السحابي

class CloudBackupManager {
  constructor() {
    this.providers = {
      google: {
        name: 'Google Drive',
        icon: '📁',
        enabled: false,
        clientId: null
      },
      dropbox: {
        name: 'Dropbox',
        icon: '📦',
        enabled: false,
        accessToken: null
      },
      local: {
        name: 'ملف محلي',
        icon: '💾',
        enabled: true
      }
    };
    
    this.backupData = {
      version: '2.0',
      timestamp: null,
      settings: {},
      statistics: {},
      achievements: {},
      userProgress: {}
    };
    
    this.loadSettings();
  }

  // تحميل إعدادات النسخ الاحتياطي
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['cloudBackupSettings']);
      if (result.cloudBackupSettings) {
        this.providers = { ...this.providers, ...result.cloudBackupSettings };
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات النسخ الاحتياطي:', error);
    }
  }

  // حفظ إعدادات النسخ الاحتياطي
  async saveSettings() {
    try {
      await chrome.storage.local.set({ cloudBackupSettings: this.providers });
    } catch (error) {
      console.error('خطأ في حفظ إعدادات النسخ الاحتياطي:', error);
    }
  }

  // جمع جميع البيانات للنسخ الاحتياطي
  async collectBackupData() {
    try {
      const allData = await chrome.storage.local.get(null);
      
      this.backupData = {
        version: '2.0',
        timestamp: new Date().toISOString(),
        settings: {
          azkarTimes: allData.azkarTimes || {},
          audioSettings: allData.audioSettings || {},
          prayerTimesSettings: allData.prayerTimesSettings || {},
          themeSettings: allData.themeSettings || {},
          hijriCalendarSettings: allData.hijriCalendarSettings || {},
          currentLanguage: allData.currentLanguage || 'ar'
        },
        statistics: {
          azkarStats: allData.azkarStats || {},
          prayerStats: allData.prayerStats || {},
          ramadanStats: allData.ramadanStats || {}
        },
        achievements: {
          unlockedAchievements: allData.unlockedAchievements || [],
          userStats: allData.userStats || {},
          challenges: allData.challenges || {}
        },
        userProgress: {
          streaks: allData.streaks || {},
          lastActivity: allData.lastActivity || {},
          preferences: allData.preferences || {}
        }
      };
      
      return this.backupData;
    } catch (error) {
      console.error('خطأ في جمع بيانات النسخ الاحتياطي:', error);
      throw error;
    }
  }

  // إنشاء نسخة احتياطية محلية
  async createLocalBackup() {
    try {
      const data = await this.collectBackupData();
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `azkar-backup-${new Date().toISOString().split('T')[0]}.json`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      this.showSuccessMessage('تم إنشاء النسخة الاحتياطية المحلية بنجاح');
      return { success: true, method: 'local' };
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية المحلية:', error);
      throw error;
    }
  }

  // استعادة من نسخة احتياطية محلية
  async restoreFromLocalBackup(file) {
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      // التحقق من صحة البيانات
      if (!this.validateBackupData(data)) {
        throw new Error('ملف النسخة الاحتياطية غير صالح');
      }
      
      // استعادة البيانات
      await this.restoreBackupData(data);
      
      this.showSuccessMessage('تم استعادة النسخة الاحتياطية بنجاح');
      return { success: true, method: 'local' };
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      throw error;
    }
  }

  // التحقق من صحة بيانات النسخة الاحتياطية
  validateBackupData(data) {
    if (!data || typeof data !== 'object') return false;
    if (!data.version || !data.timestamp) return false;
    if (!data.settings || typeof data.settings !== 'object') return false;
    
    // التحقق من الإصدار
    const supportedVersions = ['1.0', '2.0'];
    if (!supportedVersions.includes(data.version)) {
      console.warn('إصدار غير مدعوم:', data.version);
      return false;
    }
    
    return true;
  }

  // استعادة بيانات النسخة الاحتياطية
  async restoreBackupData(data) {
    try {
      // استعادة الإعدادات
      if (data.settings) {
        for (const [key, value] of Object.entries(data.settings)) {
          if (value && Object.keys(value).length > 0) {
            await chrome.storage.local.set({ [key]: value });
          }
        }
      }
      
      // استعادة الإحصائيات
      if (data.statistics) {
        for (const [key, value] of Object.entries(data.statistics)) {
          if (value && Object.keys(value).length > 0) {
            await chrome.storage.local.set({ [key]: value });
          }
        }
      }
      
      // استعادة الإنجازات
      if (data.achievements) {
        for (const [key, value] of Object.entries(data.achievements)) {
          if (value) {
            await chrome.storage.local.set({ [key]: value });
          }
        }
      }
      
      // استعادة تقدم المستخدم
      if (data.userProgress) {
        for (const [key, value] of Object.entries(data.userProgress)) {
          if (value && Object.keys(value).length > 0) {
            await chrome.storage.local.set({ [key]: value });
          }
        }
      }
      
      // إعادة تحميل الصفحة لتطبيق التغييرات
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('خطأ في استعادة البيانات:', error);
      throw error;
    }
  }

  // إنشاء نسخة احتياطية تلقائية
  async createAutoBackup() {
    try {
      const lastBackup = await this.getLastBackupTime();
      const now = new Date();
      const daysSinceLastBackup = (now - lastBackup) / (1000 * 60 * 60 * 24);
      
      // إنشاء نسخة احتياطية كل 7 أيام
      if (daysSinceLastBackup >= 7) {
        await this.createLocalBackup();
        await chrome.storage.local.set({ lastAutoBackup: now.toISOString() });
      }
    } catch (error) {
      console.error('خطأ في النسخ الاحتياطي التلقائي:', error);
    }
  }

  // الحصول على وقت آخر نسخة احتياطية
  async getLastBackupTime() {
    try {
      const result = await chrome.storage.local.get(['lastAutoBackup']);
      return result.lastAutoBackup ? new Date(result.lastAutoBackup) : new Date(0);
    } catch (error) {
      return new Date(0);
    }
  }

  // مزامنة البيانات عبر الأجهزة (محاكاة)
  async syncData() {
    try {
      // هذه وظيفة محاكاة للمزامنة
      // في التطبيق الحقيقي، ستحتاج إلى API خارجي
      
      const data = await this.collectBackupData();
      const syncKey = `azkar_sync_${Date.now()}`;
      
      // حفظ في التخزين المحلي مع مفتاح المزامنة
      await chrome.storage.local.set({ [syncKey]: data });
      
      this.showSuccessMessage('تم حفظ البيانات للمزامنة');
      return { success: true, syncKey };
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
      throw error;
    }
  }

  // إنشاء واجهة النسخ الاحتياطي
  createBackupInterface() {
    const container = document.createElement('div');
    container.className = 'backup-interface';
    container.innerHTML = `
      <div class="backup-section">
        <h3>💾 النسخ الاحتياطي والاستعادة</h3>
        
        <div class="backup-options">
          <div class="backup-option">
            <div class="backup-option-header">
              <span class="backup-icon">📁</span>
              <div class="backup-info">
                <h4>نسخة احتياطية محلية</h4>
                <p>حفظ جميع البيانات في ملف على جهازك</p>
              </div>
            </div>
            <div class="backup-actions">
              <button class="btn backup-btn" id="create-local-backup">
                إنشاء نسخة احتياطية
              </button>
              <button class="btn restore-btn" id="restore-local-backup">
                استعادة من ملف
              </button>
            </div>
          </div>
          
          <div class="backup-option">
            <div class="backup-option-header">
              <span class="backup-icon">🔄</span>
              <div class="backup-info">
                <h4>المزامنة التلقائية</h4>
                <p>مزامنة البيانات عبر الأجهزة المختلفة</p>
              </div>
            </div>
            <div class="backup-actions">
              <button class="btn sync-btn" id="sync-data">
                مزامنة البيانات
              </button>
              <button class="btn auto-backup-btn" id="toggle-auto-backup">
                تفعيل النسخ التلقائي
              </button>
            </div>
          </div>
        </div>
        
        <div class="backup-info-section">
          <h4>📊 معلومات النسخ الاحتياطي</h4>
          <div class="backup-stats">
            <div class="backup-stat">
              <span class="stat-label">آخر نسخة احتياطية:</span>
              <span class="stat-value" id="last-backup-time">لم يتم إنشاء نسخة بعد</span>
            </div>
            <div class="backup-stat">
              <span class="stat-label">حجم البيانات:</span>
              <span class="stat-value" id="data-size">جاري الحساب...</span>
            </div>
            <div class="backup-stat">
              <span class="stat-label">عدد الإعدادات:</span>
              <span class="stat-value" id="settings-count">جاري الحساب...</span>
            </div>
          </div>
        </div>
        
        <input type="file" id="backup-file-input" accept=".json" style="display: none;">
      </div>
    `;
    
    this.attachBackupEvents(container);
    return container;
  }

  // ربط أحداث واجهة النسخ الاحتياطي
  attachBackupEvents(container) {
    const createBtn = container.querySelector('#create-local-backup');
    const restoreBtn = container.querySelector('#restore-local-backup');
    const syncBtn = container.querySelector('#sync-data');
    const autoBackupBtn = container.querySelector('#toggle-auto-backup');
    const fileInput = container.querySelector('#backup-file-input');
    
    // إنشاء نسخة احتياطية
    createBtn.addEventListener('click', async () => {
      try {
        createBtn.disabled = true;
        createBtn.textContent = 'جاري الإنشاء...';
        
        await this.createLocalBackup();
        await this.updateBackupStats(container);
      } catch (error) {
        this.showErrorMessage('فشل في إنشاء النسخة الاحتياطية');
      } finally {
        createBtn.disabled = false;
        createBtn.textContent = 'إنشاء نسخة احتياطية';
      }
    });
    
    // استعادة من ملف
    restoreBtn.addEventListener('click', () => {
      fileInput.click();
    });
    
    fileInput.addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (file) {
        try {
          restoreBtn.disabled = true;
          restoreBtn.textContent = 'جاري الاستعادة...';
          
          await this.restoreFromLocalBackup(file);
        } catch (error) {
          this.showErrorMessage('فشل في استعادة النسخة الاحتياطية');
        } finally {
          restoreBtn.disabled = false;
          restoreBtn.textContent = 'استعادة من ملف';
          fileInput.value = '';
        }
      }
    });
    
    // مزامنة البيانات
    syncBtn.addEventListener('click', async () => {
      try {
        syncBtn.disabled = true;
        syncBtn.textContent = 'جاري المزامنة...';
        
        await this.syncData();
      } catch (error) {
        this.showErrorMessage('فشل في مزامنة البيانات');
      } finally {
        syncBtn.disabled = false;
        syncBtn.textContent = 'مزامنة البيانات';
      }
    });
    
    // تحديث إحصائيات النسخ الاحتياطي
    this.updateBackupStats(container);
  }

  // تحديث إحصائيات النسخ الاحتياطي
  async updateBackupStats(container) {
    try {
      const lastBackupTime = await this.getLastBackupTime();
      const data = await this.collectBackupData();
      const dataSize = new Blob([JSON.stringify(data)]).size;
      const settingsCount = Object.keys(data.settings).length;
      
      const lastBackupElement = container.querySelector('#last-backup-time');
      const dataSizeElement = container.querySelector('#data-size');
      const settingsCountElement = container.querySelector('#settings-count');
      
      if (lastBackupTime.getTime() > 0) {
        lastBackupElement.textContent = lastBackupTime.toLocaleDateString('ar-SA');
      }
      
      dataSizeElement.textContent = this.formatFileSize(dataSize);
      settingsCountElement.textContent = settingsCount;
    } catch (error) {
      console.error('خطأ في تحديث إحصائيات النسخ الاحتياطي:', error);
    }
  }

  // تنسيق حجم الملف
  formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  // عرض رسالة نجاح
  showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      if (successDiv.parentNode) {
        successDiv.parentNode.removeChild(successDiv);
      }
    }, 3000);
  }

  // عرض رسالة خطأ
  showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'audio-error-message';
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 3000);
  }
}

// إنشاء مثيل عام لمدير النسخ الاحتياطي
const cloudBackupManager = new CloudBackupManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.cloudBackupManager = cloudBackupManager;
}
