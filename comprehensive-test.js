// ملف اختبار شامل لإضافة أذكار المسلم
// يتم تشغيله في console المتصفح للتحقق من جميع الوظائف

class ComprehensiveTest {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    console.log('🚀 بدء الاختبار الشامل المحسن لإضافة أذكار المسلم');
    console.log('=' .repeat(60));

    try {
      await this.testCSPCompliance();
      await this.testEventHandlers();
      await this.testSVGElements();
      await this.testGoogleAuth();
      await this.testZikrCounting();
      await this.testThemeSwitching();
      await this.testAudioPlayback();
      await this.testDataPersistence();
      await this.testResponsiveDesign();
      await this.testErrorHandling();
      await this.testJavaScriptFunctions();
      await this.testSyntaxErrors();
      await this.testMissingFunctions();

      this.displayResults();
    } catch (error) {
      console.error('❌ خطأ في تشغيل الاختبارات:', error);
    }
  }

  // اختبار CSP Compliance
  async testCSPCompliance() {
    console.log('🔒 اختبار Content Security Policy...');

    try {
      // فحص عدم وجود inline event handlers
      const elementsWithInlineEvents = document.querySelectorAll('[onclick], [onload], [onerror], [onchange], [onsubmit]');

      if (elementsWithInlineEvents.length === 0) {
        this.addResult('CSP - Inline Events', '✅ لا توجد inline event handlers');
      } else {
        this.addResult('CSP - Inline Events', `❌ وجد ${elementsWithInlineEvents.length} inline event handlers`);
        elementsWithInlineEvents.forEach(el => {
          console.warn('عنصر يحتوي على inline event:', el);
        });
      }

      // فحص Google Fonts في CSS
      const stylesheets = Array.from(document.styleSheets);
      let googleFontsFound = false;

      try {
        stylesheets.forEach(sheet => {
          if (sheet.href && sheet.href.includes('fonts.googleapis.com')) {
            googleFontsFound = true;
          }
        });
      } catch (e) {
        // قد يفشل الوصول للـ stylesheets بسبب CORS
      }

      if (!googleFontsFound) {
        this.addResult('CSP - Google Fonts', '✅ لا يتم تحميل Google Fonts خارجياً');
      } else {
        this.addResult('CSP - Google Fonts', '❌ يتم تحميل Google Fonts خارجياً');
      }

      // فحص console للأخطاء CSP
      this.addResult('CSP - Console Errors', '✅ يجب فحص console يدوياً للتأكد من عدم وجود أخطاء CSP');

    } catch (error) {
      this.addError('CSP Test', error);
    }
  }

  // اختبار Event Handlers
  async testEventHandlers() {
    console.log('🎯 اختبار Event Handlers...');
    
    try {
      // اختبار أزرار الأذكار
      const readButtons = document.querySelectorAll('.read-button');
      const shareButtons = document.querySelectorAll('.share-button');
      const audioButtons = document.querySelectorAll('.audio-btn');

      this.addResult('Event Handlers - Read Buttons', `✅ وجد ${readButtons.length} زر قراءة`);
      this.addResult('Event Handlers - Share Buttons', `✅ وجد ${shareButtons.length} زر مشاركة`);
      this.addResult('Event Handlers - Audio Buttons', `✅ وجد ${audioButtons.length} زر صوتي`);

      // اختبار tabs
      const tabs = document.querySelectorAll('.tab');
      let tabsWorking = true;
      tabs.forEach(tab => {
        if (!tab.onclick && !this.hasEventListener(tab, 'click')) {
          tabsWorking = false;
        }
      });

      this.addResult('Event Handlers - Tabs', tabsWorking ? '✅ التبويبات تعمل' : '❌ مشكلة في التبويبات');

    } catch (error) {
      this.addError('Event Handlers Test', error);
    }
  }

  // اختبار SVG Elements
  async testSVGElements() {
    console.log('🎨 اختبار SVG Elements...');
    
    try {
      const svgElements = document.querySelectorAll('svg');
      let svgIssues = 0;

      svgElements.forEach(svg => {
        // فحص استخدام className بدلاً من setAttribute
        if (svg.className && typeof svg.className === 'string') {
          svgIssues++;
          console.warn('SVG يستخدم className بدلاً من setAttribute:', svg);
        }
      });

      if (svgIssues === 0) {
        this.addResult('SVG Elements', `✅ جميع عناصر SVG (${svgElements.length}) تستخدم setAttribute بشكل صحيح`);
      } else {
        this.addResult('SVG Elements', `❌ وجد ${svgIssues} مشاكل في عناصر SVG`);
      }

    } catch (error) {
      this.addError('SVG Test', error);
    }
  }

  // اختبار Google Auth
  async testGoogleAuth() {
    console.log('🔐 اختبار Google Authentication...');
    
    try {
      const googleAuthManager = window.googleAuthManager;
      
      if (googleAuthManager) {
        this.addResult('Google Auth - Manager', '✅ Google Auth Manager متاح');
        
        // اختبار fallback mode
        if (typeof googleAuthManager.activateFallbackMode === 'function') {
          this.addResult('Google Auth - Fallback', '✅ نظام Fallback متاح');
        } else {
          this.addResult('Google Auth - Fallback', '❌ نظام Fallback غير متاح');
        }

        // اختبار validation
        if (typeof googleAuthManager.validateClientId === 'function') {
          this.addResult('Google Auth - Validation', '✅ تحقق Client ID متاح');
        } else {
          this.addResult('Google Auth - Validation', '❌ تحقق Client ID غير متاح');
        }

      } else {
        this.addResult('Google Auth - Manager', '❌ Google Auth Manager غير متاح');
      }

    } catch (error) {
      this.addError('Google Auth Test', error);
    }
  }

  // اختبار عداد الأذكار
  async testZikrCounting() {
    console.log('📿 اختبار نظام عداد الأذكار...');
    
    try {
      // اختبار وجود الوظائف
      const functions = ['markAsRead', 'resetZikrProgress', 'updateProgressDisplay'];
      let functionsAvailable = 0;

      functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
          functionsAvailable++;
        }
      });

      this.addResult('Zikr Counting - Functions', `✅ ${functionsAvailable}/${functions.length} وظائف متاحة`);

      // اختبار عدادات الأذكار المتكررة
      const progressElements = document.querySelectorAll('.read-progress');
      this.addResult('Zikr Counting - Progress Elements', `✅ وجد ${progressElements.length} عداد تقدم`);

      // اختبار أزرار إعادة التعيين
      const resetButtons = document.querySelectorAll('.reset-zikr-button');
      this.addResult('Zikr Counting - Reset Buttons', `✅ وجد ${resetButtons.length} زر إعادة تعيين`);

    } catch (error) {
      this.addError('Zikr Counting Test', error);
    }
  }

  // اختبار تبديل الثيمات
  async testThemeSwitching() {
    console.log('🎨 اختبار تبديل الثيمات...');
    
    try {
      const themeManager = window.themeManager;
      
      if (themeManager) {
        this.addResult('Theme Switching - Manager', '✅ Theme Manager متاح');
        
        // اختبار الثيمات المتاحة
        if (typeof themeManager.getAllThemes === 'function') {
          const themes = themeManager.getAllThemes();
          this.addResult('Theme Switching - Available Themes', `✅ ${Object.keys(themes).length} ثيم متاح`);
        }

        // اختبار الوضع الليلي
        const isDarkMode = document.body.classList.contains('dark-theme');
        this.addResult('Theme Switching - Dark Mode', isDarkMode ? '🌙 الوضع الليلي نشط' : '☀️ الوضع الفاتح نشط');

      } else {
        this.addResult('Theme Switching - Manager', '❌ Theme Manager غير متاح');
      }

    } catch (error) {
      this.addError('Theme Switching Test', error);
    }
  }

  // اختبار تشغيل الصوت
  async testAudioPlayback() {
    console.log('🔊 اختبار تشغيل الصوت...');
    
    try {
      // اختبار وجود وظائف الصوت
      const audioFunctions = ['playAudio', 'stopAudio', 'playConfirmationSound', 'playProgressSound'];
      let audioFunctionsAvailable = 0;

      audioFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
          audioFunctionsAvailable++;
        }
      });

      this.addResult('Audio Playback - Functions', `✅ ${audioFunctionsAvailable}/${audioFunctions.length} وظائف صوتية متاحة`);

      // اختبار AudioContext
      try {
        const AudioContextClass = window.AudioContext || window.webkitAudioContext;
        if (AudioContextClass) {
          this.addResult('Audio Playback - AudioContext', '✅ AudioContext مدعوم');
        } else {
          this.addResult('Audio Playback - AudioContext', '❌ AudioContext غير مدعوم');
        }
      } catch (audioError) {
        this.addResult('Audio Playback - AudioContext', '❌ خطأ في AudioContext');
      }

    } catch (error) {
      this.addError('Audio Playback Test', error);
    }
  }

  // اختبار حفظ البيانات
  async testDataPersistence() {
    console.log('💾 اختبار حفظ البيانات...');
    
    try {
      // اختبار Chrome Storage API
      if (chrome && chrome.storage) {
        this.addResult('Data Persistence - Chrome Storage', '✅ Chrome Storage API متاح');
        
        // اختبار حفظ واستعادة بيانات تجريبية
        const testData = { test: 'comprehensive-test-' + Date.now() };
        
        chrome.storage.local.set(testData, () => {
          chrome.storage.local.get(['test'], (result) => {
            if (result.test === testData.test) {
              this.addResult('Data Persistence - Read/Write', '✅ حفظ واستعادة البيانات يعمل');
            } else {
              this.addResult('Data Persistence - Read/Write', '❌ مشكلة في حفظ/استعادة البيانات');
            }
            
            // تنظيف البيانات التجريبية
            chrome.storage.local.remove(['test']);
          });
        });

      } else {
        this.addResult('Data Persistence - Chrome Storage', '❌ Chrome Storage API غير متاح');
      }

    } catch (error) {
      this.addError('Data Persistence Test', error);
    }
  }

  // اختبار التصميم المتجاوب
  async testResponsiveDesign() {
    console.log('📱 اختبار التصميم المتجاوب...');
    
    try {
      const container = document.querySelector('.container');
      
      if (container) {
        const containerStyles = window.getComputedStyle(container);
        this.addResult('Responsive Design - Container', '✅ حاوية رئيسية موجودة');
        
        // اختبار media queries
        const mediaQueries = [
          '(max-width: 450px)',
          '(min-width: 768px)',
          '(max-height: 500px) and (orientation: landscape)'
        ];

        let responsiveRules = 0;
        mediaQueries.forEach(query => {
          if (window.matchMedia(query).matches) {
            responsiveRules++;
          }
        });

        this.addResult('Responsive Design - Media Queries', `✅ ${responsiveRules} قاعدة متجاوبة نشطة`);

      } else {
        this.addResult('Responsive Design - Container', '❌ حاوية رئيسية غير موجودة');
      }

    } catch (error) {
      this.addError('Responsive Design Test', error);
    }
  }

  // اختبار معالجة الأخطاء
  async testErrorHandling() {
    console.log('⚠️ اختبار معالجة الأخطاء...');
    
    try {
      // فحص console للأخطاء
      const originalError = console.error;
      let errorCount = 0;
      
      console.error = function(...args) {
        errorCount++;
        originalError.apply(console, args);
      };

      // استعادة console.error الأصلي
      setTimeout(() => {
        console.error = originalError;
        this.addResult('Error Handling - Console Errors', `📊 ${errorCount} خطأ في console`);
      }, 1000);

      // اختبار وجود try-catch blocks
      this.addResult('Error Handling - Try-Catch', '✅ يجب فحص الكود يدوياً للتأكد من وجود try-catch blocks');

    } catch (error) {
      this.addError('Error Handling Test', error);
    }
  }

  // اختبار الوظائف JavaScript
  async testJavaScriptFunctions() {
    console.log('🔧 اختبار الوظائف JavaScript...');

    try {
      // فحص الوظائف الأساسية
      const requiredFunctions = [
        'markAsRead',
        'resetZikrProgress',
        'playAudio',
        'stopAudio',
        'shareZikr',
        'updateCurrentTime',
        'loadAzkar'
      ];

      let missingFunctions = [];
      let availableFunctions = 0;

      requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
          availableFunctions++;
        } else {
          missingFunctions.push(funcName);
        }
      });

      this.addResult('JavaScript Functions - Core', `✅ ${availableFunctions}/${requiredFunctions.length} وظائف أساسية متاحة`);

      if (missingFunctions.length > 0) {
        this.addResult('JavaScript Functions - Missing', `❌ وظائف مفقودة: ${missingFunctions.join(', ')}`);
      }

      // فحص المدراء (Managers)
      const managers = [
        'themeManager',
        'googleAuthManager',
        'visualEffectsManager',
        'achievementsManager',
        'prayerTimesManager'
      ];

      let availableManagers = 0;
      let missingManagers = [];

      managers.forEach(managerName => {
        if (window[managerName]) {
          availableManagers++;
        } else {
          missingManagers.push(managerName);
        }
      });

      this.addResult('JavaScript Functions - Managers', `✅ ${availableManagers}/${managers.length} مدراء متاحين`);

      if (missingManagers.length > 0) {
        this.addResult('JavaScript Functions - Missing Managers', `⚠️ مدراء مفقودين: ${missingManagers.join(', ')}`);
      }

      // فحص وظائف المدراء المهمة
      if (window.detailedStatisticsManager) {
        const statisticsManager = window.detailedStatisticsManager;
        const requiredMethods = ['createCategoryChart', 'createTimePatternChart', 'updateWeeklyChart'];
        let availableMethods = 0;

        requiredMethods.forEach(methodName => {
          if (typeof statisticsManager[methodName] === 'function') {
            availableMethods++;
          }
        });

        this.addResult('JavaScript Functions - Statistics Manager', `✅ ${availableMethods}/${requiredMethods.length} وظائف إحصائيات متاحة`);
      } else {
        this.addResult('JavaScript Functions - Statistics Manager', '❌ مدير الإحصائيات غير متاح');
      }

    } catch (error) {
      this.addError('JavaScript Functions Test', error);
    }
  }

  // اختبار أخطاء Syntax
  async testSyntaxErrors() {
    console.log('🔍 اختبار أخطاء Syntax...');

    try {
      let syntaxErrors = 0;

      // فحص console للأخطاء
      const originalError = console.error;
      const errors = [];

      console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
      };

      // محاولة تنفيذ وظائف أساسية للتحقق من عدم وجود أخطاء syntax
      try {
        if (typeof markAsRead === 'function') {
          // لا نستدعي الوظيفة فعلياً، فقط نتحقق من وجودها
        }
        if (typeof shareZikr === 'function') {
          // لا نستدعي الوظيفة فعلياً، فقط نتحقق من وجودها
        }
        if (typeof playAudio === 'function') {
          // لا نستدعي الوظيفة فعلياً، فقط نتحقق من وجودها
        }
      } catch (syntaxError) {
        syntaxErrors++;
        this.addResult('Syntax Errors - Function Definitions', `❌ خطأ syntax في تعريف الوظائف: ${syntaxError.message}`);
      }

      // استعادة console.error الأصلي
      console.error = originalError;

      // فحص الأخطاء المسجلة
      const syntaxRelatedErrors = errors.filter(error =>
        error.includes('SyntaxError') ||
        error.includes('missing') ||
        error.includes('unexpected')
      );

      if (syntaxRelatedErrors.length === 0 && syntaxErrors === 0) {
        this.addResult('Syntax Errors', '✅ لا توجد أخطاء syntax واضحة');
      } else {
        this.addResult('Syntax Errors', `❌ وجد ${syntaxRelatedErrors.length + syntaxErrors} أخطاء syntax محتملة`);
        syntaxRelatedErrors.forEach(error => {
          console.warn('خطأ syntax محتمل:', error);
        });
      }

    } catch (error) {
      this.addError('Syntax Errors Test', error);
    }
  }

  // اختبار الوظائف المفقودة
  async testMissingFunctions() {
    console.log('🔧 اختبار الوظائف المفقودة...');

    try {
      const criticalFunctions = [
        'markAsRead',
        'shareZikr',
        'playAudio',
        'stopAudio',
        'loadAzkar',
        'updateCurrentTime',
        'resetZikrProgress'
      ];

      const criticalObjects = [
        'achievementsManager',
        'themeManager',
        'googleAuthManager',
        'detailedStatisticsManager'
      ];

      let missingFunctions = [];
      let missingObjects = [];

      // فحص الوظائف الحرجة
      criticalFunctions.forEach(funcName => {
        if (typeof window[funcName] !== 'function') {
          missingFunctions.push(funcName);
        }
      });

      // فحص الكائنات الحرجة
      criticalObjects.forEach(objName => {
        if (!window[objName]) {
          missingObjects.push(objName);
        }
      });

      // فحص وظائف محددة في الكائنات
      if (window.detailedStatisticsManager) {
        const requiredMethods = ['createAchievementsSection', 'createCategoryChart', 'createTimePatternChart'];
        requiredMethods.forEach(methodName => {
          if (typeof window.detailedStatisticsManager[methodName] !== 'function') {
            missingFunctions.push(`detailedStatisticsManager.${methodName}`);
          }
        });
      }

      // تقرير النتائج
      if (missingFunctions.length === 0) {
        this.addResult('Missing Functions - Critical Functions', '✅ جميع الوظائف الحرجة متاحة');
      } else {
        this.addResult('Missing Functions - Critical Functions', `❌ وظائف مفقودة: ${missingFunctions.join(', ')}`);
      }

      if (missingObjects.length === 0) {
        this.addResult('Missing Functions - Critical Objects', '✅ جميع الكائنات الحرجة متاحة');
      } else {
        this.addResult('Missing Functions - Critical Objects', `❌ كائنات مفقودة: ${missingObjects.join(', ')}`);
      }

      // اختبار استدعاءات الوظائف
      this.testFunctionCalls();

    } catch (error) {
      this.addError('Missing Functions Test', error);
    }
  }

  // اختبار استدعاءات الوظائف
  testFunctionCalls() {
    try {
      // اختبار استدعاء وظائف بمعاملات آمنة
      const testCalls = [
        {
          name: 'updateCurrentTime',
          test: () => typeof updateCurrentTime === 'function' && updateCurrentTime.length === 0
        },
        {
          name: 'loadAzkar',
          test: () => typeof loadAzkar === 'function'
        },
        {
          name: 'achievementsManager.getUserStats',
          test: () => window.achievementsManager && typeof window.achievementsManager.getUserStats === 'function'
        }
      ];

      let workingCalls = 0;
      let totalCalls = testCalls.length;

      testCalls.forEach(testCall => {
        try {
          if (testCall.test()) {
            workingCalls++;
          }
        } catch (error) {
          console.warn(`خطأ في اختبار ${testCall.name}:`, error);
        }
      });

      this.addResult('Function Calls Test', `✅ ${workingCalls}/${totalCalls} استدعاءات وظائف تعمل بشكل صحيح`);

    } catch (error) {
      this.addError('Function Calls Test', error);
    }
  }

  // وظائف مساعدة
  hasEventListener(element, eventType) {
    // هذه وظيفة تقريبية - في الواقع صعب جداً فحص event listeners
    return element.onclick !== null || element.addEventListener !== undefined;
  }

  addResult(test, result) {
    this.testResults.push({ test, result });
  }

  addError(test, error) {
    this.errors.push({ test, error: error.message });
  }

  // عرض النتائج
  displayResults() {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 نتائج الاختبار الشامل');
    console.log('=' .repeat(50));

    this.testResults.forEach(({ test, result }) => {
      console.log(`${test}: ${result}`);
    });

    if (this.errors.length > 0) {
      console.log('\n❌ الأخطاء المكتشفة:');
      this.errors.forEach(({ test, error }) => {
        console.error(`${test}: ${error}`);
      });
    }

    console.log('\n' + '=' .repeat(50));
    console.log(`✅ اكتمل الاختبار - ${this.testResults.length} اختبار، ${this.errors.length} خطأ`);
    console.log('=' .repeat(50));
  }
}

// تشغيل الاختبار
if (typeof window !== 'undefined') {
  window.comprehensiveTest = new ComprehensiveTest();
  
  // تشغيل تلقائي بعد تحميل الصفحة
  if (document.readyState === 'complete') {
    window.comprehensiveTest.runAllTests();
  } else {
    window.addEventListener('load', () => {
      setTimeout(() => {
        window.comprehensiveTest.runAllTests();
      }, 2000); // انتظار 2 ثانية للتأكد من تحميل جميع المكونات
    });
  }
}

// تصدير للاستخدام في Node.js إذا لزم الأمر
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ComprehensiveTest;
}
