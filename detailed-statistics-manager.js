// مدير الإحصائيات المفصلة
class DetailedStatisticsManager {
  constructor() {
    this.stats = {};
    this.charts = {};
  }

  // تهيئة مدير الإحصائيات
  async init() {
    await this.loadStatistics();
    this.setupStatisticsUI();
    this.startDataCollection();
  }

  // تحميل الإحصائيات
  async loadStatistics() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['detailedStats'], (result) => {
        this.stats = result.detailedStats || {
          dailyReads: {},
          weeklyReads: {},
          monthlyReads: {},
          categoryStats: {},
          timeStats: {},
          streakData: { current: 0, longest: 0, lastReadDate: null },
          achievements: [],
          totalReadingTime: 0,
          favoriteAzkar: {},
          readingPatterns: {}
        };
        resolve();
      });
    });
  }

  // حفظ الإحصائيات
  async saveStatistics() {
    return new Promise((resolve) => {
      chrome.storage.local.set({ detailedStats: this.stats }, resolve);
    });
  }

  // إعداد واجهة الإحصائيات
  setupStatisticsUI() {
    this.createStatisticsSection();
    this.createChartsSection();
    this.createAchievementsSection();
  }

  // إنشاء قسم الإحصائيات
  createStatisticsSection() {
    const container = document.getElementById('statistics-container');
    if (!container) return;

    container.innerHTML = `
      <div class="statistics-header">
        <h3>📊 إحصائياتك المفصلة</h3>
        <button id="refresh-stats-btn" class="btn-secondary">تحديث</button>
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📿</div>
          <div class="stat-content">
            <h4 id="total-reads">0</h4>
            <p>إجمالي الأذكار</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🔥</div>
          <div class="stat-content">
            <h4 id="current-streak">0</h4>
            <p>أيام متتالية</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⏱️</div>
          <div class="stat-content">
            <h4 id="reading-time">0</h4>
            <p>دقائق قراءة</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🏆</div>
          <div class="stat-content">
            <h4 id="achievements-count">0</h4>
            <p>إنجازات</p>
          </div>
        </div>
      </div>
      
      <div class="detailed-stats">
        <div class="stat-section">
          <h4>📈 إحصائيات الأسبوع</h4>
          <div id="weekly-chart" class="chart-container"></div>
        </div>
        
        <div class="stat-section">
          <h4>📊 الأذكار المفضلة</h4>
          <div id="favorite-azkar-list" class="favorite-list"></div>
        </div>
        
        <div class="stat-section">
          <h4>⏰ أنماط القراءة</h4>
          <div id="reading-patterns" class="patterns-container"></div>
        </div>
      </div>
    `;

    // ربط الأحداث
    document.getElementById('refresh-stats-btn')?.addEventListener('click', () => {
      this.updateStatistics();
    });
  }

  // إنشاء قسم الرسوم البيانية
  createChartsSection() {
    this.createWeeklyChart();
    this.createCategoryChart();
    this.createTimePatternChart();
  }

  // إنشاء رسم بياني أسبوعي
  createWeeklyChart() {
    const container = document.getElementById('weekly-chart');
    if (!container) return;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '200');
    svg.setAttribute('viewBox', '0 0 350 200');
    svg.setAttribute('class', 'weekly-chart');

    container.appendChild(svg);
    this.charts.weekly = svg;

    this.updateWeeklyChart();
  }

  // تحديث الرسم البياني الأسبوعي
  updateWeeklyChart() {
    const svg = this.charts.weekly;
    if (!svg) return;

    // مسح المحتوى السابق
    svg.innerHTML = '';

    // الحصول على بيانات الأسبوع
    const weekData = this.getWeeklyData();
    
    // رسم الشبكة
    this.drawGrid(svg, 350, 200);
    
    // رسم البيانات
    this.drawWeeklyBars(svg, weekData);
    
    // رسم التسميات
    this.drawWeeklyLabels(svg, weekData);
  }

  // رسم الشبكة
  drawGrid(svg, width, height) {
    // خطوط أفقية
    for (let i = 0; i <= 5; i++) {
      const y = 30 + (i * 30);
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', '40');
      line.setAttribute('y1', y);
      line.setAttribute('x2', width - 20);
      line.setAttribute('y2', y);
      line.setAttribute('stroke', '#e0e0e0');
      line.setAttribute('stroke-width', '1');
      svg.appendChild(line);
    }
  }

  // رسم أعمدة الأسبوع
  drawWeeklyBars(svg, data) {
    const barWidth = 35;
    const maxHeight = 120;
    const maxValue = Math.max(...data.map(d => d.count), 1);

    data.forEach((day, index) => {
      const x = 50 + (index * 45);
      const height = (day.count / maxValue) * maxHeight;
      const y = 150 - height;

      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('x', x);
      rect.setAttribute('y', y);
      rect.setAttribute('width', barWidth);
      rect.setAttribute('height', height);
      rect.setAttribute('fill', this.getBarColor(day.count, maxValue));
      rect.setAttribute('rx', '3');
      
      svg.appendChild(rect);

      // إضافة قيمة فوق العمود
      if (day.count > 0) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', x + barWidth / 2);
        text.setAttribute('y', y - 5);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '12');
        text.setAttribute('fill', '#666');
        text.textContent = day.count;
        svg.appendChild(text);
      }
    });
  }

  // رسم تسميات الأسبوع
  drawWeeklyLabels(svg, data) {
    data.forEach((day, index) => {
      const x = 50 + (index * 45) + 17.5; // وسط العمود

      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', x);
      text.setAttribute('y', '170');
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '10');
      text.setAttribute('fill', '#666');
      text.textContent = day.dayName.substring(0, 3); // أول 3 أحرف
      svg.appendChild(text);
    });
  }

  // إنشاء رسم بياني للفئات
  createCategoryChart() {
    const container = document.getElementById('category-chart');
    if (!container) {
      // إنشاء الحاوية إذا لم تكن موجودة
      const chartContainer = document.createElement('div');
      chartContainer.id = 'category-chart';
      chartContainer.className = 'chart-container';

      const statisticsContainer = document.getElementById('statistics-container');
      if (statisticsContainer) {
        const chartSection = document.createElement('div');
        chartSection.className = 'chart-section';
        chartSection.innerHTML = '<h4>📊 إحصائيات الفئات</h4>';
        chartSection.appendChild(chartContainer);
        statisticsContainer.appendChild(chartSection);
      }
    }

    const chartContainer = document.getElementById('category-chart');
    if (!chartContainer) return;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '250');
    svg.setAttribute('viewBox', '0 0 400 250');
    svg.setAttribute('class', 'category-chart');

    chartContainer.appendChild(svg);
    this.charts.category = svg;

    this.updateCategoryChart();
  }

  // تحديث رسم بياني الفئات
  updateCategoryChart() {
    const svg = this.charts.category;
    if (!svg) return;

    // مسح المحتوى السابق
    svg.innerHTML = '';

    // الحصول على بيانات الفئات
    const categoryData = this.getCategoryData();

    // رسم الشبكة
    this.drawGrid(svg, 400, 250);

    // رسم البيانات كـ pie chart
    this.drawCategoryPie(svg, categoryData);

    // رسم التسميات
    this.drawCategoryLabels(svg, categoryData);
  }

  // رسم pie chart للفئات
  drawCategoryPie(svg, data) {
    const centerX = 200;
    const centerY = 125;
    const radius = 80;
    const total = data.reduce((sum, item) => sum + item.count, 0);

    if (total === 0) return;

    let currentAngle = 0;
    const colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];

    data.forEach((category, index) => {
      const percentage = category.count / total;
      const angle = percentage * 2 * Math.PI;

      const x1 = centerX + radius * Math.cos(currentAngle);
      const y1 = centerY + radius * Math.sin(currentAngle);
      const x2 = centerX + radius * Math.cos(currentAngle + angle);
      const y2 = centerY + radius * Math.sin(currentAngle + angle);

      const largeArcFlag = angle > Math.PI ? 1 : 0;

      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');

      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('d', pathData);
      path.setAttribute('fill', colors[index % colors.length]);
      path.setAttribute('stroke', '#fff');
      path.setAttribute('stroke-width', '2');

      svg.appendChild(path);

      currentAngle += angle;
    });
  }

  // رسم تسميات الفئات
  drawCategoryLabels(svg, data) {
    const total = data.reduce((sum, item) => sum + item.count, 0);

    data.forEach((category, index) => {
      const y = 30 + (index * 25);
      const color = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'][index % 5];

      // مربع اللون
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('x', '320');
      rect.setAttribute('y', y - 10);
      rect.setAttribute('width', '15');
      rect.setAttribute('height', '15');
      rect.setAttribute('fill', color);
      svg.appendChild(rect);

      // النص
      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', '340');
      text.setAttribute('y', y);
      text.setAttribute('font-size', '12');
      text.setAttribute('fill', '#666');
      text.textContent = `${category.name}: ${category.count}`;
      svg.appendChild(text);
    });
  }

  // الحصول على بيانات الفئات
  getCategoryData() {
    const categories = [
      { name: 'أذكار الصباح', count: 0 },
      { name: 'أذكار المساء', count: 0 },
      { name: 'أذكار بعد الصلاة', count: 0 },
      { name: 'أذكار رمضان', count: 0 }
    ];

    // حساب الإحصائيات من البيانات المحفوظة
    chrome.storage.local.get(['azkarStats'], (result) => {
      if (result.azkarStats) {
        categories[0].count = result.azkarStats.morning || 0;
        categories[1].count = result.azkarStats.evening || 0;
        categories[2].count = result.azkarStats.prayer || 0;
        categories[3].count = result.azkarStats.ramadan || 0;
      }
    });

    return categories;
  }

  // إنشاء رسم بياني لأنماط الوقت
  createTimePatternChart() {
    const container = document.getElementById('time-pattern-chart');
    if (!container) {
      // إنشاء الحاوية إذا لم تكن موجودة
      const chartContainer = document.createElement('div');
      chartContainer.id = 'time-pattern-chart';
      chartContainer.className = 'chart-container';

      const statisticsContainer = document.getElementById('statistics-container');
      if (statisticsContainer) {
        const chartSection = document.createElement('div');
        chartSection.className = 'chart-section';
        chartSection.innerHTML = '<h4>⏰ أنماط الوقت</h4>';
        chartSection.appendChild(chartContainer);
        statisticsContainer.appendChild(chartSection);
      }
    }

    const chartContainer = document.getElementById('time-pattern-chart');
    if (!chartContainer) return;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '200');
    svg.setAttribute('viewBox', '0 0 400 200');
    svg.setAttribute('class', 'time-pattern-chart');

    chartContainer.appendChild(svg);
    this.charts.timePattern = svg;

    this.updateTimePatternChart();
  }

  // تحديث رسم بياني أنماط الوقت
  updateTimePatternChart() {
    const svg = this.charts.timePattern;
    if (!svg) return;

    // مسح المحتوى السابق
    svg.innerHTML = '';

    // الحصول على بيانات أنماط الوقت
    const timeData = this.getTimePatternData();

    // رسم الشبكة
    this.drawGrid(svg, 400, 200);

    // رسم البيانات
    this.drawTimePatternBars(svg, timeData);

    // رسم التسميات
    this.drawTimePatternLabels(svg, timeData);
  }

  // رسم أعمدة أنماط الوقت
  drawTimePatternBars(svg, data) {
    const barWidth = 30;
    const maxHeight = 120;
    const maxValue = Math.max(...data.map(d => d.count), 1);

    data.forEach((timeSlot, index) => {
      const x = 50 + (index * 40);
      const height = (timeSlot.count / maxValue) * maxHeight;
      const y = 150 - height;

      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('x', x);
      rect.setAttribute('y', y);
      rect.setAttribute('width', barWidth);
      rect.setAttribute('height', height);
      rect.setAttribute('fill', this.getTimeBarColor(timeSlot.hour));
      rect.setAttribute('rx', '3');

      svg.appendChild(rect);

      // إضافة قيمة فوق العمود
      if (timeSlot.count > 0) {
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', x + barWidth / 2);
        text.setAttribute('y', y - 5);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '10');
        text.setAttribute('fill', '#666');
        text.textContent = timeSlot.count;
        svg.appendChild(text);
      }
    });
  }

  // رسم تسميات أنماط الوقت
  drawTimePatternLabels(svg, data) {
    data.forEach((timeSlot, index) => {
      const x = 50 + (index * 40) + 15; // وسط العمود

      const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      text.setAttribute('x', x);
      text.setAttribute('y', '170');
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('font-size', '9');
      text.setAttribute('fill', '#666');
      text.textContent = `${timeSlot.hour}:00`;
      svg.appendChild(text);
    });
  }

  // الحصول على بيانات أنماط الوقت
  getTimePatternData() {
    const timeSlots = [];

    // إنشاء فترات زمنية (كل 4 ساعات)
    for (let hour = 0; hour < 24; hour += 4) {
      timeSlots.push({
        hour: hour,
        count: Math.floor(Math.random() * 10) // بيانات تجريبية - يجب استبدالها ببيانات حقيقية
      });
    }

    return timeSlots;
  }

  // الحصول على لون عمود الوقت
  getTimeBarColor(hour) {
    if (hour >= 5 && hour < 12) return '#FFD700'; // صباح
    if (hour >= 12 && hour < 17) return '#FF9800'; // ظهر
    if (hour >= 17 && hour < 21) return '#2196F3'; // مساء
    return '#9C27B0'; // ليل
  }

  // الحصول على لون العمود
  getBarColor(value, maxValue) {
    const intensity = value / maxValue;
    if (intensity >= 0.8) return '#4CAF50';
    if (intensity >= 0.6) return '#8BC34A';
    if (intensity >= 0.4) return '#CDDC39';
    if (intensity >= 0.2) return '#FFC107';
    return '#FF9800';
  }

  // الحصول على بيانات الأسبوع
  getWeeklyData() {
    const weekData = [];
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toDateString();
      const dayName = dayNames[date.getDay()];
      
      const count = this.stats.dailyReads[dateString] || 0;
      
      weekData.push({
        date: dateString,
        dayName: dayName,
        count: count
      });
    }
    
    return weekData;
  }

  // تسجيل قراءة ذكر
  recordZikrRead(zikrId, category, readingTime = 30) {
    const today = new Date().toDateString();
    const now = new Date();
    const hour = now.getHours();

    // تحديث الإحصائيات اليومية
    this.stats.dailyReads[today] = (this.stats.dailyReads[today] || 0) + 1;

    // تحديث إحصائيات الفئات
    if (!this.stats.categoryStats[category]) {
      this.stats.categoryStats[category] = 0;
    }
    this.stats.categoryStats[category]++;

    // تحديث إحصائيات الوقت
    if (!this.stats.timeStats[hour]) {
      this.stats.timeStats[hour] = 0;
    }
    this.stats.timeStats[hour]++;

    // تحديث الأذكار المفضلة
    if (!this.stats.favoriteAzkar[zikrId]) {
      this.stats.favoriteAzkar[zikrId] = { count: 0, category: category };
    }
    this.stats.favoriteAzkar[zikrId].count++;

    // تحديث وقت القراءة الإجمالي
    this.stats.totalReadingTime += readingTime;

    // تحديث بيانات السلسلة
    this.updateStreakData(today);

    // حفظ الإحصائيات
    this.saveStatistics();

    // تحديث الواجهة
    this.updateStatisticsDisplay();
  }

  // تحديث بيانات السلسلة
  updateStreakData(today) {
    const lastReadDate = this.stats.streakData.lastReadDate;
    
    if (!lastReadDate) {
      // أول قراءة
      this.stats.streakData.current = 1;
      this.stats.streakData.longest = 1;
    } else {
      const lastDate = new Date(lastReadDate);
      const currentDate = new Date(today);
      const daysDiff = Math.floor((currentDate - lastDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === 1) {
        // يوم متتالي
        this.stats.streakData.current++;
        if (this.stats.streakData.current > this.stats.streakData.longest) {
          this.stats.streakData.longest = this.stats.streakData.current;
        }
      } else if (daysDiff > 1) {
        // انقطعت السلسلة
        this.stats.streakData.current = 1;
      }
      // إذا كان daysDiff === 0، فهو نفس اليوم، لا نغير شيء
    }
    
    this.stats.streakData.lastReadDate = today;
  }

  // تحديث عرض الإحصائيات
  updateStatisticsDisplay() {
    // إجمالي القراءات
    const totalReads = Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0);
    document.getElementById('total-reads').textContent = totalReads;

    // السلسلة الحالية
    document.getElementById('current-streak').textContent = this.stats.streakData.current;

    // وقت القراءة
    const readingMinutes = Math.floor(this.stats.totalReadingTime / 60);
    document.getElementById('reading-time').textContent = readingMinutes;

    // عدد الإنجازات
    document.getElementById('achievements-count').textContent = this.stats.achievements.length;

    // تحديث الأذكار المفضلة
    this.updateFavoriteAzkarDisplay();

    // تحديث أنماط القراءة
    this.updateReadingPatternsDisplay();

    // تحديث الرسم البياني
    this.updateWeeklyChart();
  }

  // تحديث عرض الأذكار المفضلة
  updateFavoriteAzkarDisplay() {
    const container = document.getElementById('favorite-azkar-list');
    if (!container) return;

    const sortedAzkar = Object.entries(this.stats.favoriteAzkar)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 5); // أفضل 5

    container.innerHTML = sortedAzkar.map(([zikrId, data], index) => `
      <div class="favorite-item">
        <span class="rank">${index + 1}</span>
        <span class="category">${data.category}</span>
        <span class="count">${data.count} مرة</span>
      </div>
    `).join('');
  }

  // تحديث عرض أنماط القراءة
  updateReadingPatternsDisplay() {
    const container = document.getElementById('reading-patterns');
    if (!container) return;

    // العثور على أكثر الأوقات قراءة
    const bestHour = Object.entries(this.stats.timeStats)
      .sort(([,a], [,b]) => b - a)[0];

    const bestTime = bestHour ? this.formatHour(parseInt(bestHour[0])) : 'غير محدد';
    
    // حساب متوسط القراءات اليومية
    const totalDays = Object.keys(this.stats.dailyReads).length;
    const avgDaily = totalDays > 0 ? 
      (Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0) / totalDays).toFixed(1) : 0;

    container.innerHTML = `
      <div class="pattern-item">
        <span class="pattern-label">أفضل وقت للقراءة:</span>
        <span class="pattern-value">${bestTime}</span>
      </div>
      <div class="pattern-item">
        <span class="pattern-label">متوسط القراءات اليومية:</span>
        <span class="pattern-value">${avgDaily}</span>
      </div>
      <div class="pattern-item">
        <span class="pattern-label">أطول سلسلة:</span>
        <span class="pattern-value">${this.stats.streakData.longest} يوم</span>
      </div>
    `;
  }

  // تنسيق الساعة
  formatHour(hour) {
    if (hour >= 6 && hour < 12) return `${hour}:00 صباحاً`;
    if (hour >= 12 && hour < 18) return `${hour}:00 ظهراً`;
    if (hour >= 18 && hour < 24) return `${hour}:00 مساءً`;
    return `${hour}:00 ليلاً`;
  }

  // تحديث الإحصائيات
  updateStatistics() {
    this.updateStatisticsDisplay();
    
    // إظهار رسالة تأكيد
    const btn = document.getElementById('refresh-stats-btn');
    if (btn) {
      const originalText = btn.textContent;
      btn.textContent = 'تم التحديث ✓';
      btn.disabled = true;
      
      setTimeout(() => {
        btn.textContent = originalText;
        btn.disabled = false;
      }, 2000);
    }
  }

  // بدء جمع البيانات
  startDataCollection() {
    // الاستماع لأحداث قراءة الأذكار
    document.addEventListener('zikrRead', (event) => {
      const { zikrId, category } = event.detail;
      this.recordZikrRead(zikrId, category);
    });
  }

  // الحصول على تقرير مفصل
  generateDetailedReport() {
    const totalReads = Object.values(this.stats.dailyReads).reduce((sum, count) => sum + count, 0);
    const totalDays = Object.keys(this.stats.dailyReads).length;
    const avgDaily = totalDays > 0 ? (totalReads / totalDays).toFixed(1) : 0;
    
    return {
      summary: {
        totalReads,
        totalDays,
        avgDaily,
        currentStreak: this.stats.streakData.current,
        longestStreak: this.stats.streakData.longest,
        totalReadingTime: Math.floor(this.stats.totalReadingTime / 60)
      },
      categories: this.stats.categoryStats,
      timePatterns: this.stats.timeStats,
      favoriteAzkar: this.stats.favoriteAzkar,
      weeklyData: this.getWeeklyData()
    };
  }

  // إنشاء قسم الإنجازات
  createAchievementsSection() {
    const container = document.getElementById('statistics-container');
    if (!container) return;

    // إنشاء قسم الإنجازات إذا لم يكن موجوداً
    let achievementsSection = document.getElementById('achievements-stats-section');
    if (!achievementsSection) {
      achievementsSection = document.createElement('div');
      achievementsSection.id = 'achievements-stats-section';
      achievementsSection.className = 'achievements-stats-section';

      achievementsSection.innerHTML = `
        <div class="section-header">
          <h4>🏆 إحصائيات الإنجازات</h4>
          <button id="refresh-achievements-btn" class="btn-secondary">تحديث</button>
        </div>
        <div class="achievements-overview">
          <div class="achievement-stat">
            <div class="stat-icon">🏅</div>
            <div class="stat-value" id="total-achievements">0</div>
            <div class="stat-label">إجمالي الإنجازات</div>
          </div>
          <div class="achievement-stat">
            <div class="stat-icon">⭐</div>
            <div class="stat-value" id="total-points-earned">0</div>
            <div class="stat-label">النقاط المكتسبة</div>
          </div>
          <div class="achievement-stat">
            <div class="stat-icon">📈</div>
            <div class="stat-value" id="current-level">1</div>
            <div class="stat-label">المستوى الحالي</div>
          </div>
          <div class="achievement-stat">
            <div class="stat-icon">🔥</div>
            <div class="stat-value" id="current-streak-display">0</div>
            <div class="stat-label">الأيام المتتالية</div>
          </div>
        </div>
        <div class="achievements-progress">
          <h5>📊 تقدم الإنجازات</h5>
          <div id="achievements-progress-chart" class="chart-container">
            <!-- سيتم إنشاء الرسم البياني هنا -->
          </div>
        </div>
        <div class="recent-achievements-stats">
          <h5>🌟 الإنجازات الحديثة</h5>
          <div id="recent-achievements-list" class="recent-achievements-list">
            <!-- سيتم عرض الإنجازات الحديثة هنا -->
          </div>
        </div>
      `;

      container.appendChild(achievementsSection);
    }

    // إعداد event listeners
    this.setupAchievementsEventListeners();

    // تحديث البيانات
    this.updateAchievementsStats();
  }

  // إعداد event listeners لقسم الإنجازات
  setupAchievementsEventListeners() {
    const refreshBtn = document.getElementById('refresh-achievements-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.updateAchievementsStats();
      });
    }
  }

  // تحديث إحصائيات الإنجازات
  async updateAchievementsStats() {
    try {
      if (!window.achievementsManager) {
        console.warn('مدير الإنجازات غير متاح');
        return;
      }

      const stats = achievementsManager.getUserStats();

      // تحديث الإحصائيات الأساسية
      const totalAchievements = document.getElementById('total-achievements');
      const totalPoints = document.getElementById('total-points-earned');
      const currentLevel = document.getElementById('current-level');
      const currentStreak = document.getElementById('current-streak-display');

      if (totalAchievements) totalAchievements.textContent = stats.achievementsCount || 0;
      if (totalPoints) totalPoints.textContent = stats.totalPoints || 0;
      if (currentLevel) currentLevel.textContent = stats.level || 1;
      if (currentStreak) currentStreak.textContent = stats.currentStreak || 0;

      // تحديث الإنجازات الحديثة
      await this.updateRecentAchievementsList();

      // تحديث رسم بياني الإنجازات
      this.updateAchievementsChart();

    } catch (error) {
      console.error('خطأ في تحديث إحصائيات الإنجازات:', error);
    }
  }

  // تحديث قائمة الإنجازات الحديثة
  async updateRecentAchievementsList() {
    const recentList = document.getElementById('recent-achievements-list');
    if (!recentList || !window.achievementsManager) return;

    try {
      const recentAchievements = await achievementsManager.getRecentAchievements();

      if (recentAchievements.length === 0) {
        recentList.innerHTML = '<div class="no-recent-achievements">لا توجد إنجازات حديثة</div>';
        return;
      }

      recentList.innerHTML = recentAchievements.slice(0, 3).map(achievement => `
        <div class="recent-achievement-item">
          <div class="achievement-icon">${achievement.icon}</div>
          <div class="achievement-details">
            <div class="achievement-name">${achievement.name}</div>
            <div class="achievement-points">+${achievement.points} نقطة</div>
          </div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في تحديث قائمة الإنجازات الحديثة:', error);
      recentList.innerHTML = '<div class="error-message">خطأ في تحميل الإنجازات</div>';
    }
  }

  // تحديث رسم بياني الإنجازات
  updateAchievementsChart() {
    const chartContainer = document.getElementById('achievements-progress-chart');
    if (!chartContainer || !window.achievementsManager) return;

    try {
      // مسح المحتوى السابق
      chartContainer.innerHTML = '';

      // إنشاء رسم بياني بسيط للتقدم
      const stats = achievementsManager.getUserStats();
      const progressPercentage = Math.min((stats.levelProgress || 0), 100);

      chartContainer.innerHTML = `
        <div class="level-progress-bar">
          <div class="progress-label">التقدم نحو المستوى التالي</div>
          <div class="progress-bar-container">
            <div class="progress-bar-fill" style="width: ${progressPercentage}%"></div>
          </div>
          <div class="progress-text">${progressPercentage}%</div>
        </div>
      `;

    } catch (error) {
      console.error('خطأ في تحديث رسم بياني الإنجازات:', error);
    }
  }
}

// إنشاء مثيل عام
window.detailedStatisticsManager = new DetailedStatisticsManager();
