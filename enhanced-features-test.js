// اختبار الميزات المحسنة
class EnhancedFeaturesTest {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  // تشغيل جميع اختبارات الميزات المحسنة
  async runEnhancedTests() {
    console.log('🚀 بدء اختبار الميزات المحسنة...');
    console.log('=' .repeat(60));
    
    this.testResults = [];
    this.errors = [];

    try {
      await this.testEnhancedPointsSystem();
      await this.testGoogleProfileIntegration();
      await this.testAchievementSystem();
      await this.testVisualEffects();
      await this.testPerformanceOptimizations();
      await this.testResponsiveDesign();
      await this.testErrorHandling();
      
      this.displayResults();
    } catch (error) {
      console.error('❌ خطأ في تشغيل اختبارات الميزات المحسنة:', error);
    }
  }

  // اختبار نظام النقاط المحسن
  async testEnhancedPointsSystem() {
    console.log('💎 اختبار نظام النقاط المحسن...');
    
    try {
      // فحص وجود النظام
      if (window.enhancedPointsSystem) {
        this.addResult('Enhanced Points System - Availability', '✅ نظام النقاط المحسن متاح');
        
        // فحص الوظائف الأساسية
        const requiredMethods = ['addPoints', 'calculatePoints', 'updateUI', 'checkNewAchievements', 'getAllAchievements'];
        let availableMethods = 0;
        
        requiredMethods.forEach(method => {
          if (typeof window.enhancedPointsSystem[method] === 'function') {
            availableMethods++;
          }
        });
        
        this.addResult('Enhanced Points System - Methods', `✅ ${availableMethods}/${requiredMethods.length} وظائف متاحة`);
        
        // فحص عرض النقاط
        const pointsDisplay = document.getElementById('points-display');
        if (pointsDisplay) {
          this.addResult('Enhanced Points System - UI', '✅ واجهة النقاط معروضة');
          
          // فحص عناصر الواجهة
          const uiElements = ['points-value', 'level-badge', 'level-progress'];
          let foundUIElements = 0;
          uiElements.forEach(elementId => {
            if (document.getElementById(elementId)) {
              foundUIElements++;
            }
          });
          
          this.addResult('Enhanced Points System - UI Elements', `✅ ${foundUIElements}/${uiElements.length} عناصر واجهة`);
        } else {
          this.addResult('Enhanced Points System - UI', '❌ واجهة النقاط غير معروضة');
        }
        
        // اختبار حساب النقاط
        try {
          const pointsData = window.enhancedPointsSystem.calculatePoints('medium', 'morning', 'morning');
          if (pointsData && pointsData.finalPoints > 0) {
            this.addResult('Enhanced Points System - Calculation', '✅ حساب النقاط يعمل');
          } else {
            this.addResult('Enhanced Points System - Calculation', '❌ مشكلة في حساب النقاط');
          }
        } catch (calcError) {
          this.addResult('Enhanced Points System - Calculation', '❌ خطأ في حساب النقاط');
        }
        
      } else {
        this.addResult('Enhanced Points System - Availability', '❌ نظام النقاط المحسن غير متاح');
      }
      
    } catch (error) {
      this.addError('Enhanced Points System Test', error);
    }
  }

  // اختبار تكامل Google Profile
  async testGoogleProfileIntegration() {
    console.log('👤 اختبار تكامل Google Profile...');
    
    try {
      if (window.googleAuthManager) {
        // فحص الوظائف الجديدة
        const newMethods = ['updateUserProfileImage', 'createInitialsAvatar', 'updateProfileImageGlobally', 'cacheProfileImage'];
        let availableNewMethods = 0;
        
        newMethods.forEach(method => {
          if (typeof window.googleAuthManager[method] === 'function') {
            availableNewMethods++;
          }
        });
        
        this.addResult('Google Profile Integration - Methods', `✅ ${availableNewMethods}/${newMethods.length} وظائف محسنة متاحة`);
        
        // فحص عناصر الصورة الشخصية
        const profileElements = [
          'header-profile-image',
          'profile-image-preview',
          'google-user-photo'
        ];
        
        let foundElements = 0;
        profileElements.forEach(elementId => {
          if (document.getElementById(elementId)) {
            foundElements++;
          }
        });
        
        this.addResult('Google Profile Integration - Elements', `✅ ${foundElements}/${profileElements.length} عناصر الصورة موجودة`);
        
        // فحص النص الترحيبي
        const welcomeMessage = document.getElementById('welcome-message');
        if (welcomeMessage) {
          this.addResult('Google Profile Integration - Welcome Message', '✅ رسالة الترحيب موجودة');
        } else {
          this.addResult('Google Profile Integration - Welcome Message', '❌ رسالة الترحيب غير موجودة');
        }
        
      } else {
        this.addResult('Google Profile Integration', '❌ مدير Google Auth غير متاح');
      }
      
    } catch (error) {
      this.addError('Google Profile Integration Test', error);
    }
  }

  // اختبار نظام الإنجازات
  async testAchievementSystem() {
    console.log('🏆 اختبار نظام الإنجازات...');
    
    try {
      if (window.enhancedPointsSystem) {
        // فحص الإنجازات المتاحة
        const achievements = window.enhancedPointsSystem.getAllAchievements();
        this.addResult('Achievement System - Available Achievements', `✅ ${achievements.length} إنجاز متاح`);
        
        // فحص أنواع الإنجازات
        const achievementTypes = ['points', 'streak', 'variety', 'early_bird', 'night_owl'];
        const foundTypes = achievementTypes.filter(type => 
          achievements.some(achievement => achievement.id.includes(type))
        );
        
        this.addResult('Achievement System - Types', `✅ ${foundTypes.length}/${achievementTypes.length} أنواع إنجازات`);
        
        // فحص وظائف الإنجازات
        const achievementMethods = ['checkNewAchievements', 'hasAchievement', 'checkAchievementCondition'];
        let availableAchievementMethods = 0;
        
        achievementMethods.forEach(method => {
          if (typeof window.enhancedPointsSystem[method] === 'function') {
            availableAchievementMethods++;
          }
        });
        
        this.addResult('Achievement System - Methods', `✅ ${availableAchievementMethods}/${achievementMethods.length} وظائف إنجازات`);
        
        // فحص شروط الإنجازات
        let validConditions = 0;
        achievements.forEach(achievement => {
          try {
            if (typeof achievement.condition === 'function') {
              validConditions++;
            }
          } catch (e) {
            // تجاهل الأخطاء في الشروط
          }
        });
        
        this.addResult('Achievement System - Conditions', `✅ ${validConditions}/${achievements.length} شروط صحيحة`);
        
      } else {
        this.addResult('Achievement System', '❌ نظام الإنجازات غير متاح');
      }
      
    } catch (error) {
      this.addError('Achievement System Test', error);
    }
  }

  // اختبار التأثيرات البصرية
  async testVisualEffects() {
    console.log('✨ اختبار التأثيرات البصرية...');
    
    try {
      // فحص مدير التأثيرات البصرية
      if (window.visualEffectsManager) {
        this.addResult('Visual Effects - Manager', '✅ مدير التأثيرات البصرية متاح');
        
        // فحص الوظائف
        const effectMethods = ['createParticleEffect', 'showCelebrationEffect', 'applyGlowEffect', 'pulseCounter'];
        let availableEffectMethods = 0;
        
        effectMethods.forEach(method => {
          if (typeof window.visualEffectsManager[method] === 'function') {
            availableEffectMethods++;
          }
        });
        
        this.addResult('Visual Effects - Methods', `✅ ${availableEffectMethods}/${effectMethods.length} وظائف تأثيرات`);
        
      } else {
        this.addResult('Visual Effects - Manager', '❌ مدير التأثيرات البصرية غير متاح');
      }
      
      // فحص CSS animations
      const animationStyles = document.querySelectorAll('style');
      let hasAnimations = false;
      let animationCount = 0;
      
      animationStyles.forEach(style => {
        const keyframeMatches = style.textContent.match(/@keyframes/g);
        if (keyframeMatches) {
          hasAnimations = true;
          animationCount += keyframeMatches.length;
        }
      });
      
      if (hasAnimations) {
        this.addResult('Visual Effects - CSS Animations', `✅ ${animationCount} CSS animations موجودة`);
      } else {
        this.addResult('Visual Effects - CSS Animations', '❌ CSS animations غير موجودة');
      }
      
      // فحص تأثيرات النقاط
      if (window.enhancedPointsSystem) {
        const animationMethods = ['showPointsAnimation', 'showLevelUpAnimation', 'showAchievementAnimation'];
        let availableAnimationMethods = 0;
        
        animationMethods.forEach(method => {
          if (typeof window.enhancedPointsSystem[method] === 'function') {
            availableAnimationMethods++;
          }
        });
        
        this.addResult('Visual Effects - Points Animations', `✅ ${availableAnimationMethods}/${animationMethods.length} تأثيرات نقاط`);
      }
      
    } catch (error) {
      this.addError('Visual Effects Test', error);
    }
  }

  // اختبار تحسينات الأداء
  async testPerformanceOptimizations() {
    console.log('⚡ اختبار تحسينات الأداء...');
    
    try {
      const startTime = performance.now();
      
      // فحص سرعة تحميل الصفحة
      const loadTime = performance.now() - startTime;
      if (loadTime < 50) {
        this.addResult('Performance - Load Time', `✅ وقت التحميل ممتاز: ${loadTime.toFixed(2)}ms`);
      } else if (loadTime < 100) {
        this.addResult('Performance - Load Time', `✅ وقت التحميل جيد: ${loadTime.toFixed(2)}ms`);
      } else {
        this.addResult('Performance - Load Time', `⚠️ وقت التحميل بطيء: ${loadTime.toFixed(2)}ms`);
      }
      
      // فحص استهلاك الذاكرة
      if (performance.memory) {
        const memoryUsage = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
        if (memoryUsage < 30) {
          this.addResult('Performance - Memory Usage', `✅ استهلاك ذاكرة ممتاز: ${memoryUsage}MB`);
        } else if (memoryUsage < 50) {
          this.addResult('Performance - Memory Usage', `✅ استهلاك ذاكرة جيد: ${memoryUsage}MB`);
        } else {
          this.addResult('Performance - Memory Usage', `⚠️ استهلاك ذاكرة عالي: ${memoryUsage}MB`);
        }
      }
      
      // فحص عدد العناصر في DOM
      const domElements = document.querySelectorAll('*').length;
      if (domElements < 500) {
        this.addResult('Performance - DOM Elements', `✅ عناصر DOM قليلة: ${domElements}`);
      } else if (domElements < 1000) {
        this.addResult('Performance - DOM Elements', `✅ عناصر DOM معقولة: ${domElements}`);
      } else {
        this.addResult('Performance - DOM Elements', `⚠️ عناصر DOM كثيرة: ${domElements}`);
      }
      
      // فحص Event Listeners
      const elementsWithListeners = document.querySelectorAll('[data-listener]').length;
      this.addResult('Performance - Event Listeners', `✅ Event listeners: ${elementsWithListeners}`);
      
    } catch (error) {
      this.addError('Performance Test', error);
    }
  }

  // اختبار التصميم المتجاوب
  async testResponsiveDesign() {
    console.log('📱 اختبار التصميم المتجاوب...');
    
    try {
      // فحص CSS media queries
      const stylesheets = document.querySelectorAll('style, link[rel="stylesheet"]');
      let hasMediaQueries = false;
      
      stylesheets.forEach(stylesheet => {
        try {
          const cssText = stylesheet.textContent || '';
          if (cssText.includes('@media')) {
            hasMediaQueries = true;
          }
        } catch (e) {
          // تجاهل الأخطاء في قراءة CSS
        }
      });
      
      if (hasMediaQueries) {
        this.addResult('Responsive Design - Media Queries', '✅ Media queries موجودة');
      } else {
        this.addResult('Responsive Design - Media Queries', '❌ Media queries غير موجودة');
      }
      
      // فحص viewport meta tag
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      if (viewportMeta) {
        this.addResult('Responsive Design - Viewport Meta', '✅ Viewport meta tag موجود');
      } else {
        this.addResult('Responsive Design - Viewport Meta', '❌ Viewport meta tag غير موجود');
      }
      
      // فحص الحد الأدنى للعرض
      const container = document.querySelector('.container');
      if (container) {
        const containerWidth = container.offsetWidth;
        if (containerWidth >= 350) {
          this.addResult('Responsive Design - Container Width', `✅ عرض الحاوية مناسب: ${containerWidth}px`);
        } else {
          this.addResult('Responsive Design - Container Width', `⚠️ عرض الحاوية ضيق: ${containerWidth}px`);
        }
      }
      
    } catch (error) {
      this.addError('Responsive Design Test', error);
    }
  }

  // اختبار معالجة الأخطاء
  async testErrorHandling() {
    console.log('🛡️ اختبار معالجة الأخطاء...');
    
    try {
      // فحص try-catch blocks في النظام المحسن
      if (window.enhancedPointsSystem) {
        // محاولة استدعاء وظيفة مع بيانات خاطئة
        try {
          window.enhancedPointsSystem.calculatePoints(null, null, null);
          this.addResult('Error Handling - Points System', '✅ معالجة أخطاء النقاط تعمل');
        } catch (e) {
          this.addResult('Error Handling - Points System', '❌ مشكلة في معالجة أخطاء النقاط');
        }
      }
      
      // فحص معالجة أخطاء Google Auth
      if (window.googleAuthManager) {
        // فحص وجود وظائف معالجة الأخطاء
        const errorHandlingMethods = ['handleSignInError', 'showFallbackProfileImage', 'activateFallbackMode'];
        let availableErrorMethods = 0;
        
        errorHandlingMethods.forEach(method => {
          if (typeof window.googleAuthManager[method] === 'function') {
            availableErrorMethods++;
          }
        });
        
        this.addResult('Error Handling - Google Auth', `✅ ${availableErrorMethods}/${errorHandlingMethods.length} وظائف معالجة أخطاء`);
      }
      
      // فحص console errors
      const originalConsoleError = console.error;
      let errorCount = 0;
      
      console.error = function(...args) {
        errorCount++;
        originalConsoleError.apply(console, args);
      };
      
      // استعادة console.error الأصلي
      setTimeout(() => {
        console.error = originalConsoleError;
        if (errorCount === 0) {
          this.addResult('Error Handling - Console Errors', '✅ لا توجد أخطاء في console');
        } else {
          this.addResult('Error Handling - Console Errors', `⚠️ ${errorCount} أخطاء في console`);
        }
      }, 1000);
      
    } catch (error) {
      this.addError('Error Handling Test', error);
    }
  }

  // عرض النتائج
  displayResults() {
    console.log('\n' + '🎯 نتائج اختبار الميزات المحسنة:');
    console.log('=' .repeat(60));
    
    // عرض النتائج الناجحة
    const successfulTests = this.testResults.filter(result => result.result.includes('✅'));
    const warningTests = this.testResults.filter(result => result.result.includes('⚠️'));
    const failedTests = this.testResults.filter(result => result.result.includes('❌'));
    
    console.log(`✅ اختبارات ناجحة: ${successfulTests.length}`);
    console.log(`⚠️ تحذيرات: ${warningTests.length}`);
    console.log(`❌ اختبارات فاشلة: ${failedTests.length}`);
    console.log(`🔥 أخطاء: ${this.errors.length}`);
    
    console.log('\n📋 تفاصيل النتائج:');
    this.testResults.forEach(result => {
      console.log(`${result.result} - ${result.test}`);
    });
    
    if (this.errors.length > 0) {
      console.log('\n❌ الأخطاء:');
      this.errors.forEach(error => {
        console.log(`❌ ${error.test}: ${error.error}`);
      });
    }

    console.log('\n' + '=' .repeat(60));
    console.log(`✅ اكتمل اختبار الميزات المحسنة - ${this.testResults.length} اختبار، ${this.errors.length} خطأ`);
    console.log('=' .repeat(60));
  }

  // إضافة نتيجة اختبار
  addResult(test, result) {
    this.testResults.push({ test, result });
  }

  // إضافة خطأ
  addError(test, error) {
    this.errors.push({ test, error: error.message });
  }
}

// إنشاء مثيل عام
window.enhancedFeaturesTest = new EnhancedFeaturesTest();
