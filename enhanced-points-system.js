// نظام النقاط والإنجازات المحسن
class EnhancedPointsSystem {
  constructor() {
    this.points = 0;
    this.level = 1;
    this.achievements = [];
    this.streakData = { current: 0, longest: 0, lastReadDate: null };
    this.multipliers = {
      consistency: 1.0,
      variety: 1.0,
      streak: 1.0,
      timeBonus: 1.0
    };
  }

  // تهيئة النظام
  async init() {
    await this.loadPointsData();
    this.setupPointsUI();
    this.checkDailyReset();
  }

  // تحميل بيانات النقاط
  async loadPointsData() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['enhancedPoints'], (result) => {
        if (result.enhancedPoints) {
          this.points = result.enhancedPoints.points || 0;
          this.level = result.enhancedPoints.level || 1;
          this.achievements = result.enhancedPoints.achievements || [];
          this.streakData = result.enhancedPoints.streakData || { current: 0, longest: 0, lastReadDate: null };
        }
        resolve();
      });
    });
  }

  // حفظ بيانات النقاط
  async savePointsData() {
    const data = {
      points: this.points,
      level: this.level,
      achievements: this.achievements,
      streakData: this.streakData,
      lastUpdated: Date.now()
    };
    
    return new Promise((resolve) => {
      chrome.storage.sync.set({ enhancedPoints: data }, resolve);
    });
  }

  // حساب النقاط للذكر
  calculatePoints(zikrType, category, timeOfDay, isConsecutive = false) {
    let basePoints = this.getBasePoints(zikrType);
    
    // مضاعف التنوع
    const varietyMultiplier = this.calculateVarietyMultiplier(category);
    
    // مضاعف الوقت
    const timeMultiplier = this.calculateTimeMultiplier(timeOfDay, category);
    
    // مضاعف التتالي
    const streakMultiplier = this.calculateStreakMultiplier();
    
    // مضاعف المستوى
    const levelMultiplier = 1 + (this.level * 0.1);
    
    const finalPoints = Math.round(
      basePoints * varietyMultiplier * timeMultiplier * streakMultiplier * levelMultiplier
    );
    
    return {
      basePoints,
      finalPoints,
      multipliers: {
        variety: varietyMultiplier,
        time: timeMultiplier,
        streak: streakMultiplier,
        level: levelMultiplier
      }
    };
  }

  // النقاط الأساسية حسب نوع الذكر
  getBasePoints(zikrType) {
    const pointsMap = {
      'simple': 5,      // أذكار بسيطة
      'medium': 10,     // أذكار متوسطة
      'complex': 15,    // أذكار طويلة
      'special': 20,    // أذكار خاصة (رمضان، حج، إلخ)
      'rare': 25        // أذكار نادرة
    };
    
    return pointsMap[zikrType] || 10;
  }

  // حساب مضاعف التنوع
  calculateVarietyMultiplier(category) {
    const today = new Date().toDateString();
    const todayCategories = this.getTodayCategories();
    
    if (!todayCategories.includes(category)) {
      // أول مرة في هذه الفئة اليوم
      return 1.5;
    } else if (todayCategories.length >= 3) {
      // قرأ من 3 فئات مختلفة اليوم
      return 1.3;
    }
    
    return 1.0;
  }

  // حساب مضاعف الوقت
  calculateTimeMultiplier(timeOfDay, category) {
    const hour = new Date().getHours();
    
    // أوقات مستحبة للأذكار
    const preferredTimes = {
      'morning': { start: 5, end: 9, multiplier: 1.5 },
      'evening': { start: 17, end: 20, multiplier: 1.5 },
      'night': { start: 21, end: 23, multiplier: 1.3 },
      'tahajjud': { start: 2, end: 5, multiplier: 2.0 }
    };
    
    // فحص إذا كان الوقت مناسب للفئة
    if (category === 'morning' && hour >= 5 && hour <= 9) return 1.5;
    if (category === 'evening' && hour >= 17 && hour <= 20) return 1.5;
    if (category === 'prayer' && this.isAfterPrayerTime()) return 1.4;
    
    // أوقات عامة مستحبة
    for (const [timeName, timeData] of Object.entries(preferredTimes)) {
      if (hour >= timeData.start && hour <= timeData.end) {
        return timeData.multiplier;
      }
    }
    
    return 1.0;
  }

  // حساب مضاعف التتالي
  calculateStreakMultiplier() {
    if (this.streakData.current >= 30) return 2.0;
    if (this.streakData.current >= 14) return 1.7;
    if (this.streakData.current >= 7) return 1.5;
    if (this.streakData.current >= 3) return 1.3;
    return 1.0;
  }

  // إضافة نقاط للمستخدم
  async addPoints(zikrType, category, showAnimation = true) {
    const timeOfDay = this.getCurrentTimeCategory();
    const pointsData = this.calculatePoints(zikrType, category, timeOfDay);
    
    this.points += pointsData.finalPoints;
    
    // تحديث التتالي
    this.updateStreak();
    
    // فحص ترقية المستوى
    const newLevel = this.checkLevelUp();
    
    // فحص الإنجازات الجديدة
    const newAchievements = this.checkNewAchievements();
    
    // حفظ البيانات
    await this.savePointsData();
    
    // عرض التأثيرات البصرية
    if (showAnimation) {
      this.showPointsAnimation(pointsData);
      
      if (newLevel > this.level) {
        this.showLevelUpAnimation(newLevel);
      }
      
      if (newAchievements.length > 0) {
        this.showAchievementAnimation(newAchievements);
      }
    }
    
    // تحديث الواجهة
    this.updatePointsUI();
    
    return {
      pointsEarned: pointsData.finalPoints,
      totalPoints: this.points,
      level: this.level,
      newAchievements: newAchievements
    };
  }

  // تحديث التتالي
  updateStreak() {
    const today = new Date().toDateString();
    const lastReadDate = this.streakData.lastReadDate;
    
    if (lastReadDate !== today) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (lastReadDate === yesterday.toDateString()) {
        // استمرار التتالي
        this.streakData.current++;
      } else {
        // انقطاع التتالي
        this.streakData.current = 1;
      }
      
      this.streakData.lastReadDate = today;
      
      // تحديث أطول تتالي
      if (this.streakData.current > this.streakData.longest) {
        this.streakData.longest = this.streakData.current;
      }
    }
  }

  // فحص ترقية المستوى
  checkLevelUp() {
    const requiredPoints = this.getRequiredPointsForLevel(this.level + 1);
    
    if (this.points >= requiredPoints) {
      this.level++;
      return this.level;
    }
    
    return this.level;
  }

  // النقاط المطلوبة للمستوى
  getRequiredPointsForLevel(level) {
    return Math.pow(level, 2) * 100; // 100, 400, 900, 1600, ...
  }

  // فحص الإنجازات الجديدة
  checkNewAchievements() {
    const newAchievements = [];
    const allAchievements = this.getAllAchievements();
    
    allAchievements.forEach(achievement => {
      if (!this.hasAchievement(achievement.id) && this.checkAchievementCondition(achievement)) {
        this.achievements.push({
          id: achievement.id,
          unlockedAt: Date.now(),
          points: achievement.points
        });
        newAchievements.push(achievement);
      }
    });
    
    return newAchievements;
  }

  // جميع الإنجازات المتاحة
  getAllAchievements() {
    return [
      // إنجازات النقاط
      { id: 'points_100', name: 'البداية', description: 'اكسب 100 نقطة', icon: '🌟', points: 50, condition: () => this.points >= 100 },
      { id: 'points_500', name: 'المثابر', description: 'اكسب 500 نقطة', icon: '⭐', points: 100, condition: () => this.points >= 500 },
      { id: 'points_1000', name: 'المجتهد', description: 'اكسب 1000 نقطة', icon: '🏆', points: 200, condition: () => this.points >= 1000 },
      
      // إنجازات التتالي
      { id: 'streak_3', name: 'المواظب', description: '3 أيام متتالية', icon: '🔥', points: 75, condition: () => this.streakData.current >= 3 },
      { id: 'streak_7', name: 'الملتزم', description: 'أسبوع كامل', icon: '💪', points: 150, condition: () => this.streakData.current >= 7 },
      { id: 'streak_30', name: 'الصابر', description: 'شهر كامل', icon: '👑', points: 500, condition: () => this.streakData.current >= 30 },
      
      // إنجازات التنوع
      { id: 'variety_all', name: 'المتنوع', description: 'اقرأ من جميع الفئات في يوم واحد', icon: '🌈', points: 100, condition: () => this.getTodayCategories().length >= 4 },
      
      // إنجازات الوقت
      { id: 'early_bird', name: 'المبكر', description: 'اقرأ أذكار الصباح قبل الساعة 7', icon: '🌅', points: 75, condition: () => this.hasEarlyMorningRead() },
      { id: 'night_owl', name: 'قيام الليل', description: 'اقرأ الأذكار بين 2-5 صباحاً', icon: '🌙', points: 150, condition: () => this.hasNightRead() }
    ];
  }

  // فحص شرط الإنجاز
  checkAchievementCondition(achievement) {
    try {
      return achievement.condition();
    } catch (error) {
      console.warn('خطأ في فحص شرط الإنجاز:', achievement.id, error);
      return false;
    }
  }

  // فحص وجود الإنجاز
  hasAchievement(achievementId) {
    return this.achievements.some(a => a.id === achievementId);
  }

  // الحصول على فئات اليوم
  getTodayCategories() {
    const today = new Date().toDateString();
    // هذه دالة مؤقتة - يجب ربطها بنظام تتبع القراءة الفعلي
    return JSON.parse(localStorage.getItem(`categories_${today}`) || '[]');
  }

  // إضافة فئة لليوم
  addTodayCategory(category) {
    const today = new Date().toDateString();
    const categories = this.getTodayCategories();
    if (!categories.includes(category)) {
      categories.push(category);
      localStorage.setItem(`categories_${today}`, JSON.stringify(categories));
    }
  }

  // فحص قراءة الصباح المبكر
  hasEarlyMorningRead() {
    const today = new Date().toDateString();
    const earlyReads = JSON.parse(localStorage.getItem(`early_reads_${today}`) || '[]');
    return earlyReads.length > 0;
  }

  // فحص قراءة الليل
  hasNightRead() {
    const today = new Date().toDateString();
    const nightReads = JSON.parse(localStorage.getItem(`night_reads_${today}`) || '[]');
    return nightReads.length > 0;
  }

  // الحصول على فئة الوقت الحالي
  getCurrentTimeCategory() {
    const hour = new Date().getHours();
    
    if (hour >= 5 && hour <= 9) return 'morning';
    if (hour >= 12 && hour <= 14) return 'noon';
    if (hour >= 17 && hour <= 20) return 'evening';
    if (hour >= 21 || hour <= 2) return 'night';
    if (hour >= 2 && hour <= 5) return 'tahajjud';
    
    return 'general';
  }

  // فحص إذا كان بعد وقت الصلاة
  isAfterPrayerTime() {
    // هذه دالة مؤقتة - يجب ربطها بنظام أوقات الصلاة
    const now = new Date();
    const minutes = now.getHours() * 60 + now.getMinutes();
    
    // أوقات تقريبية للصلوات (يجب تحديثها حسب الموقع)
    const prayerTimes = [
      { name: 'فجر', time: 5 * 60 },
      { name: 'ظهر', time: 12 * 60 + 30 },
      { name: 'عصر', time: 15 * 60 + 30 },
      { name: 'مغرب', time: 18 * 60 },
      { name: 'عشاء', time: 19 * 60 + 30 }
    ];
    
    // فحص إذا كان خلال 30 دقيقة من أي صلاة
    return prayerTimes.some(prayer => {
      const diff = minutes - prayer.time;
      return diff >= 0 && diff <= 30;
    });
  }

  // فحص إعادة تعيين يومي
  checkDailyReset() {
    const today = new Date().toDateString();
    const lastReset = localStorage.getItem('lastDailyReset');
    
    if (lastReset !== today) {
      // إعادة تعيين البيانات اليومية
      this.resetDailyData();
      localStorage.setItem('lastDailyReset', today);
    }
  }

  // إعادة تعيين البيانات اليومية
  resetDailyData() {
    const today = new Date().toDateString();
    
    // مسح فئات اليوم السابق
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    localStorage.removeItem(`categories_${yesterday.toDateString()}`);
    localStorage.removeItem(`early_reads_${yesterday.toDateString()}`);
    localStorage.removeItem(`night_reads_${yesterday.toDateString()}`);
  }

  // إعداد واجهة النقاط
  setupPointsUI() {
    this.createPointsDisplay();
    this.createLevelDisplay();
    this.createStreakDisplay();
    this.createAchievementsDisplay();
  }

  // إنشاء عرض النقاط
  createPointsDisplay() {
    const pointsContainer = document.createElement('div');
    pointsContainer.id = 'points-display';
    pointsContainer.className = 'points-display';
    pointsContainer.innerHTML = `
      <div class="points-header">
        <div class="points-icon">💎</div>
        <div class="points-info">
          <div class="points-value" id="points-value">${this.points}</div>
          <div class="points-label">نقطة</div>
        </div>
      </div>
      <div class="level-info">
        <div class="level-badge" id="level-badge">المستوى ${this.level}</div>
        <div class="level-progress">
          <div class="progress-bar">
            <div class="progress-fill" id="level-progress"></div>
          </div>
          <div class="progress-text" id="progress-text">0/100</div>
        </div>
      </div>
    `;

    // إضافة إلى الصفحة الرئيسية
    const mainSection = document.getElementById('main');
    if (mainSection) {
      mainSection.insertBefore(pointsContainer, mainSection.firstChild);
    }

    this.updateLevelProgress();
  }

  // تحديث شريط تقدم المستوى
  updateLevelProgress() {
    const currentLevelPoints = this.getRequiredPointsForLevel(this.level);
    const nextLevelPoints = this.getRequiredPointsForLevel(this.level + 1);
    const progressPoints = this.points - currentLevelPoints;
    const requiredPoints = nextLevelPoints - currentLevelPoints;

    const progressPercentage = Math.min((progressPoints / requiredPoints) * 100, 100);

    const progressFill = document.getElementById('level-progress');
    const progressText = document.getElementById('progress-text');

    if (progressFill) {
      progressFill.style.width = `${progressPercentage}%`;
    }

    if (progressText) {
      progressText.textContent = `${progressPoints}/${requiredPoints}`;
    }
  }

  // عرض تأثير النقاط
  showPointsAnimation(pointsData) {
    const pointsDisplay = document.getElementById('points-display');
    if (!pointsDisplay) return;

    // إنشاء عنصر التأثير
    const animation = document.createElement('div');
    animation.className = 'points-animation';
    animation.innerHTML = `
      <div class="points-earned">+${pointsData.finalPoints}</div>
      <div class="points-breakdown">
        <div class="base-points">أساسي: ${pointsData.basePoints}</div>
        ${pointsData.multipliers.variety > 1 ? `<div class="multiplier">تنوع: ×${pointsData.multipliers.variety}</div>` : ''}
        ${pointsData.multipliers.time > 1 ? `<div class="multiplier">وقت: ×${pointsData.multipliers.time}</div>` : ''}
        ${pointsData.multipliers.streak > 1 ? `<div class="multiplier">تتالي: ×${pointsData.multipliers.streak}</div>` : ''}
      </div>
    `;

    animation.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 15px 20px;
      border-radius: 15px;
      box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
      z-index: 1000;
      animation: pointsPopup 2s ease-out forwards;
      text-align: center;
      min-width: 200px;
    `;

    document.body.appendChild(animation);

    // إزالة التأثير بعد انتهاء الحركة
    setTimeout(() => {
      if (animation.parentNode) {
        animation.parentNode.removeChild(animation);
      }
    }, 2000);

    // تحديث قيمة النقاط مع تأثير
    this.animatePointsCounter(pointsData.finalPoints);
  }

  // تحريك عداد النقاط
  animatePointsCounter(pointsToAdd) {
    const pointsValue = document.getElementById('points-value');
    if (!pointsValue) return;

    const startPoints = this.points - pointsToAdd;
    const endPoints = this.points;
    const duration = 1000; // مللي ثانية
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const currentPoints = Math.round(startPoints + (pointsToAdd * progress));
      pointsValue.textContent = currentPoints;

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.updateLevelProgress();
      }
    };

    animate();
  }

  // عرض تأثير ترقية المستوى
  showLevelUpAnimation(newLevel) {
    const levelUpAnimation = document.createElement('div');
    levelUpAnimation.className = 'level-up-animation';
    levelUpAnimation.innerHTML = `
      <div class="level-up-content">
        <div class="level-up-icon">🎉</div>
        <div class="level-up-title">ترقية!</div>
        <div class="level-up-text">وصلت إلى المستوى ${newLevel}</div>
        <div class="level-up-reward">مكافأة: +${newLevel * 50} نقطة</div>
      </div>
    `;

    levelUpAnimation.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
      animation: levelUpFade 3s ease-out forwards;
    `;

    document.body.appendChild(levelUpAnimation);

    // إضافة مكافأة الترقية
    this.points += newLevel * 50;
    this.savePointsData();

    // إزالة التأثير
    setTimeout(() => {
      if (levelUpAnimation.parentNode) {
        levelUpAnimation.parentNode.removeChild(levelUpAnimation);
      }
    }, 3000);

    // تحديث شارة المستوى
    const levelBadge = document.getElementById('level-badge');
    if (levelBadge) {
      levelBadge.textContent = `المستوى ${newLevel}`;
      levelBadge.style.animation = 'levelBadgePulse 1s ease-out';
    }
  }

  // عرض تأثير الإنجاز
  showAchievementAnimation(achievements) {
    achievements.forEach((achievement, index) => {
      setTimeout(() => {
        const achievementAnimation = document.createElement('div');
        achievementAnimation.className = 'achievement-animation';
        achievementAnimation.innerHTML = `
          <div class="achievement-content">
            <div class="achievement-icon">${achievement.icon}</div>
            <div class="achievement-title">إنجاز جديد!</div>
            <div class="achievement-name">${achievement.name}</div>
            <div class="achievement-description">${achievement.description}</div>
            <div class="achievement-points">+${achievement.points} نقطة</div>
          </div>
        `;

        achievementAnimation.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
          color: white;
          padding: 20px;
          border-radius: 15px;
          box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
          z-index: 1500;
          animation: achievementSlide 4s ease-out forwards;
          max-width: 300px;
          text-align: center;
        `;

        document.body.appendChild(achievementAnimation);

        // إزالة التأثير
        setTimeout(() => {
          if (achievementAnimation.parentNode) {
            achievementAnimation.parentNode.removeChild(achievementAnimation);
          }
        }, 4000);

      }, index * 500); // تأخير بين الإنجازات المتعددة
    });
  }

  // تحديث واجهة النقاط
  updatePointsUI() {
    const pointsValue = document.getElementById('points-value');
    const levelBadge = document.getElementById('level-badge');

    if (pointsValue) {
      pointsValue.textContent = this.points;
    }

    if (levelBadge) {
      levelBadge.textContent = `المستوى ${this.level}`;
    }

    this.updateLevelProgress();
    this.updateStreakDisplay();
  }

  // تحديث عرض التتالي
  updateStreakDisplay() {
    const streakDisplay = document.getElementById('streak-display');
    if (streakDisplay) {
      streakDisplay.innerHTML = `
        <div class="streak-current">🔥 ${this.streakData.current} يوم</div>
        <div class="streak-longest">🏆 أطول تتالي: ${this.streakData.longest} يوم</div>
      `;
    }
  }

  // إضافة الأنماط CSS
  addPointsStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .points-display {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin: 10px 0;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      .points-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
      }

      .points-icon {
        font-size: 32px;
        margin-left: 15px;
      }

      .points-value {
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
      }

      .points-label {
        font-size: 14px;
        opacity: 0.9;
      }

      .level-badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        margin-bottom: 10px;
        display: inline-block;
      }

      .progress-bar {
        background: rgba(255, 255, 255, 0.2);
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 5px;
      }

      .progress-fill {
        background: linear-gradient(90deg, #4CAF50, #45a049);
        height: 100%;
        transition: width 0.5s ease;
        border-radius: 4px;
      }

      .progress-text {
        font-size: 12px;
        opacity: 0.9;
        text-align: center;
      }

      @keyframes pointsPopup {
        0% {
          transform: translate(-50%, -50%) scale(0.5);
          opacity: 0;
        }
        50% {
          transform: translate(-50%, -50%) scale(1.1);
          opacity: 1;
        }
        100% {
          transform: translate(-50%, -50%) scale(1) translateY(-50px);
          opacity: 0;
        }
      }

      @keyframes levelUpFade {
        0% { opacity: 0; }
        20% { opacity: 1; }
        80% { opacity: 1; }
        100% { opacity: 0; }
      }

      @keyframes achievementSlide {
        0% {
          transform: translateX(100%);
          opacity: 0;
        }
        20% {
          transform: translateX(0);
          opacity: 1;
        }
        80% {
          transform: translateX(0);
          opacity: 1;
        }
        100% {
          transform: translateX(100%);
          opacity: 0;
        }
      }

      @keyframes levelBadgePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }

      .points-earned {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .points-breakdown {
        font-size: 12px;
        opacity: 0.9;
      }

      .multiplier {
        margin: 2px 0;
        color: #FFD700;
      }

      .level-up-content {
        background: white;
        color: #333;
        padding: 40px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      }

      .level-up-icon {
        font-size: 64px;
        margin-bottom: 20px;
      }

      .level-up-title {
        font-size: 32px;
        font-weight: bold;
        color: #4CAF50;
        margin-bottom: 10px;
      }

      .level-up-text {
        font-size: 18px;
        margin-bottom: 15px;
      }

      .level-up-reward {
        font-size: 16px;
        color: #FF6B6B;
        font-weight: bold;
      }

      .achievement-content {
        text-align: center;
      }

      .achievement-icon {
        font-size: 48px;
        margin-bottom: 15px;
      }

      .achievement-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .achievement-name {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .achievement-description {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 10px;
      }

      .achievement-points {
        font-size: 14px;
        color: #FFD700;
        font-weight: bold;
      }
    `;

    document.head.appendChild(style);
  }
}

// إنشاء مثيل عام
window.enhancedPointsSystem = new EnhancedPointsSystem();
