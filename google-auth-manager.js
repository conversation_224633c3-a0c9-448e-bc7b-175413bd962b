// مدير تسجيل الدخول بـ Google
class GoogleAuthManager {
  constructor() {
    this.isSignedIn = false;
    this.userInfo = null;
    this.accessToken = null;
  }

  // تهيئة مدير Google Auth
  async init() {
    try {
      // التحقق من حالة تسجيل الدخول المحفوظة
      const result = await chrome.storage.sync.get(['googleAuth']);
      if (result.googleAuth) {
        this.userInfo = result.googleAuth.userInfo;
        this.isSignedIn = true;
        this.updateUI();
      }
    } catch (error) {
      console.error('خطأ في تهيئة Google Auth:', error);
    }
  }

  // تسجيل الدخول بـ Google
  async signIn() {
    try {
      // التحقق من توفر Chrome Identity API
      if (!chrome.identity) {
        this.showMessage('ميزة تسجيل الدخول بـ Google غير متاحة في هذا المتصفح', 'error');
        this.activateFallbackMode();
        return false;
      }

      // التحقق من إعدادات OAuth2
      const manifest = chrome.runtime.getManifest();
      if (!manifest.oauth2) {
        this.showMessage('إعدادات Google OAuth2 غير موجودة في manifest.json', 'error');
        this.showSetupInstructions();
        this.activateFallbackMode();
        return false;
      }

      // التحقق من صحة client_id
      if (!manifest.oauth2.client_id || manifest.oauth2.client_id.includes('PLACEHOLDER')) {
        this.showMessage('يجب إعداد Google Client ID صحيح في manifest.json', 'error');
        this.showSetupInstructions();
        this.activateFallbackMode();
        return false;
      }

      // التحقق من صحة تنسيق client_id
      if (!this.validateClientId(manifest.oauth2.client_id)) {
        this.showMessage('تنسيق Google Client ID غير صحيح', 'error');
        this.showSetupInstructions();
        this.activateFallbackMode();
        return false;
      }

      this.showMessage('جاري تسجيل الدخول...', 'info');

      // طلب الحصول على رمز الوصول مع timeout
      const token = await this.getAuthTokenWithTimeout();

      if (token) {
        this.accessToken = token;

        // الحصول على معلومات المستخدم
        const userInfo = await this.getUserInfo(token);

        if (userInfo) {
          this.userInfo = userInfo;
          this.isSignedIn = true;

          // حفظ معلومات المستخدم
          await this.saveAuthData();

          // تحديث الواجهة
          this.updateUI();

          // عرض رسالة نجاح
          this.showMessage('تم تسجيل الدخول بنجاح!', 'success');

          return true;
        } else {
          this.showMessage('فشل في الحصول على معلومات المستخدم', 'error');
          this.activateFallbackMode();
          return false;
        }
      } else {
        this.showMessage('فشل في الحصول على رمز الوصول', 'error');
        this.activateFallbackMode();
        return false;
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      return this.handleSignInError(error);
    }
  }

  // تسجيل الخروج
  async signOut() {
    try {
      if (this.accessToken) {
        // إلغاء رمز الوصول
        await chrome.identity.removeCachedAuthToken({ 
          token: this.accessToken 
        });
      }
      
      // مسح البيانات المحفوظة
      await chrome.storage.sync.remove(['googleAuth']);
      
      // إعادة تعيين الحالة
      this.isSignedIn = false;
      this.userInfo = null;
      this.accessToken = null;
      
      // تحديث الواجهة
      this.updateUI();
      
      this.showMessage('تم تسجيل الخروج بنجاح', 'success');
      
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      this.showMessage('حدث خطأ أثناء تسجيل الخروج', 'error');
    }
  }

  // التحقق من صحة تنسيق client_id
  validateClientId(clientId) {
    // تنسيق Google Client ID: xxxxxx.apps.googleusercontent.com
    const clientIdRegex = /^[0-9]+-[a-zA-Z0-9]+\.apps\.googleusercontent\.com$/;
    return clientIdRegex.test(clientId);
  }

  // الحصول على رمز الوصول مع timeout محسن
  async getAuthTokenWithTimeout(timeoutMs = 45000) {
    return new Promise((resolve, reject) => {
      let timeoutId;
      let authCompleted = false;

      // إعداد timeout مع رسالة تقدم
      const progressMessages = [
        { time: 10000, message: 'جاري الاتصال بخوادم Google...' },
        { time: 20000, message: 'يرجى التحقق من اتصال الإنترنت...' },
        { time: 30000, message: 'قد يستغرق الأمر وقتاً أطول من المعتاد...' }
      ];

      // عرض رسائل التقدم
      progressMessages.forEach(({ time, message }) => {
        setTimeout(() => {
          if (!authCompleted) {
            this.showMessage(message, 'info');
          }
        }, time);
      });

      timeoutId = setTimeout(() => {
        if (!authCompleted) {
          authCompleted = true;
          this.showMessage('انتهت مهلة تسجيل الدخول. سيتم تفعيل الوضع المحلي.', 'warning');
          reject(new Error('انتهت مهلة تسجيل الدخول - تم تفعيل الوضع المحلي'));
        }
      }, timeoutMs);

      // محاولة الحصول على رمز الوصول
      chrome.identity.getAuthToken({ interactive: true })
        .then(token => {
          if (!authCompleted) {
            authCompleted = true;
            clearTimeout(timeoutId);
            this.showMessage('تم تسجيل الدخول بنجاح!', 'success');
            resolve(token);
          }
        })
        .catch(error => {
          if (!authCompleted) {
            authCompleted = true;
            clearTimeout(timeoutId);

            // تحسين معالجة الأخطاء
            if (error.message && error.message.includes('network')) {
              this.showMessage('خطأ في الشبكة. سيتم تفعيل الوضع المحلي.', 'warning');
            } else if (error.message && error.message.includes('User did not approve')) {
              this.showMessage('تم إلغاء تسجيل الدخول. سيتم تفعيل الوضع المحلي.', 'info');
            } else {
              this.showMessage('فشل تسجيل الدخول. سيتم تفعيل الوضع المحلي.', 'warning');
            }

            reject(error);
          }
        });
    });
  }

  // معالجة أخطاء تسجيل الدخول
  handleSignInError(error) {
    // معالجة أنواع مختلفة من الأخطاء
    if (error.message && error.message.includes('OAuth2')) {
      this.showMessage('خطأ في إعدادات OAuth2. يرجى مراجعة إعدادات Google Cloud Console.', 'error');
      this.showSetupInstructions();
    } else if (error.message && error.message.includes('User did not approve')) {
      this.showMessage('تم إلغاء تسجيل الدخول من قبل المستخدم', 'info');
    } else if (error.message && error.message.includes('انتهت مهلة')) {
      this.showMessage('انتهت مهلة تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
    } else if (error.message && error.message.includes('network')) {
      this.showMessage('خطأ في الاتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.', 'error');
    } else if (error.message && error.message.includes('invalid_client')) {
      this.showMessage('معرف العميل غير صحيح. يرجى مراجعة إعدادات Google Cloud Console.', 'error');
      this.showSetupInstructions();
    } else {
      this.showMessage('فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
    }

    this.activateFallbackMode();
    return false;
  }

  // تفعيل الوضع البديل عند فشل Google Auth
  activateFallbackMode() {
    this.showMessage('تم تفعيل الوضع المحلي. يمكنك استخدام جميع مميزات الإضافة بدون تسجيل الدخول.', 'info');

    // إخفاء أزرار Google Auth وإظهار رسالة بديلة
    const googleLogin = document.getElementById('google-login');
    const googleProfile = document.getElementById('google-profile');

    if (googleLogin) {
      googleLogin.style.display = 'none';
    }

    if (googleProfile) {
      googleProfile.style.display = 'none';
    }

    // إضافة رسالة الوضع البديل
    this.showFallbackMessage();
  }

  // عرض رسالة الوضع البديل
  showFallbackMessage() {
    const authSection = document.getElementById('google-auth-section');
    if (authSection) {
      const fallbackDiv = document.createElement('div');
      fallbackDiv.className = 'fallback-mode-message';
      fallbackDiv.innerHTML = `
        <div class="fallback-content">
          <h4>🔒 الوضع المحلي نشط</h4>
          <p>يتم حفظ بياناتك محلياً على هذا الجهاز فقط.</p>
          <div class="fallback-features">
            <p>✅ جميع مميزات الأذكار متاحة</p>
            <p>✅ حفظ التقدم والإحصائيات</p>
            <p>✅ الثيمات والإعدادات</p>
            <p>❌ المزامنة عبر الأجهزة غير متاحة</p>
          </div>
          <button class="retry-google-auth-btn" id="retry-google-auth-btn">
            إعادة محاولة تسجيل الدخول بـ Google
          </button>
        </div>
      `;
      authSection.appendChild(fallbackDiv);

      // إضافة event listener للزر
      setTimeout(() => {
        const retryBtn = document.getElementById('retry-google-auth-btn');
        if (retryBtn) {
          retryBtn.addEventListener('click', () => this.signIn());
        }
      }, 100);
    }
  }

  // الحصول على معلومات المستخدم من Google API مع retry logic
  async getUserInfo(token, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 1000 * (retryCount + 1); // تأخير متزايد

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // timeout 15 ثانية

      this.showMessage(`جاري الحصول على معلومات المستخدم... (محاولة ${retryCount + 1})`, 'info');

      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        this.showMessage('تم الحصول على معلومات المستخدم بنجاح!', 'success');
        return await response.json();
      } else if (response.status === 401) {
        // Token منتهي الصلاحية
        throw new Error('TOKEN_EXPIRED');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('انتهت مهلة الحصول على معلومات المستخدم');
        this.showMessage('انتهت مهلة الحصول على معلومات المستخدم', 'warning');
      } else if (error.message === 'TOKEN_EXPIRED') {
        console.error('انتهت صلاحية رمز الوصول');
        this.showMessage('انتهت صلاحية رمز الوصول. يرجى تسجيل الدخول مرة أخرى.', 'warning');
        // إزالة الرمز المنتهي الصلاحية
        chrome.identity.removeCachedAuthToken({ token: token });
        return null;
      } else {
        console.error('خطأ في الحصول على معلومات المستخدم:', error);

        // إعادة المحاولة إذا لم نصل للحد الأقصى
        if (retryCount < maxRetries) {
          this.showMessage(`فشل في الحصول على المعلومات. إعادة المحاولة خلال ${retryDelay/1000} ثانية...`, 'warning');

          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return this.getUserInfo(token, retryCount + 1);
        } else {
          this.showMessage('فشل في الحصول على معلومات المستخدم بعد عدة محاولات', 'error');
        }
      }
    }
    return null;
  }

  // حفظ بيانات المصادقة
  async saveAuthData() {
    try {
      await chrome.storage.sync.set({
        googleAuth: {
          userInfo: this.userInfo,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('خطأ في حفظ بيانات المصادقة:', error);
    }
  }

  // مزامنة البيانات مع Google Drive
  async syncData() {
    if (!this.isSignedIn || !this.accessToken) {
      this.showMessage('يجب تسجيل الدخول أولاً', 'error');
      return false;
    }

    try {
      this.showMessage('جاري مزامنة البيانات...', 'info');
      
      // الحصول على جميع البيانات المحلية
      const localData = await chrome.storage.sync.get(null);
      
      // إنشاء ملف النسخ الاحتياطي
      const backupData = {
        timestamp: Date.now(),
        version: '1.0',
        data: localData
      };
      
      // رفع البيانات إلى Google Drive
      const success = await this.uploadToGoogleDrive(backupData);
      
      if (success) {
        this.showMessage('تم مزامنة البيانات بنجاح!', 'success');
        return true;
      } else {
        this.showMessage('فشل في مزامنة البيانات', 'error');
        return false;
      }
      
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
      this.showMessage('حدث خطأ أثناء المزامنة', 'error');
      return false;
    }
  }

  // رفع البيانات إلى Google Drive
  async uploadToGoogleDrive(data) {
    try {
      const fileName = `azkar-backup-${Date.now()}.json`;
      const fileContent = JSON.stringify(data, null, 2);
      
      // إنشاء metadata للملف
      const metadata = {
        name: fileName,
        parents: ['appDataFolder']
      };
      
      // رفع الملف
      const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'multipart/related; boundary="foo_bar_baz"'
        },
        body: this.createMultipartBody(metadata, fileContent)
      });
      
      return response.ok;
      
    } catch (error) {
      console.error('خطأ في رفع البيانات:', error);
      return false;
    }
  }

  // إنشاء محتوى multipart للرفع
  createMultipartBody(metadata, data) {
    const delimiter = 'foo_bar_baz';
    const close_delim = `\r\n--${delimiter}--`;
    
    let body = `--${delimiter}\r\n`;
    body += 'Content-Type: application/json\r\n\r\n';
    body += JSON.stringify(metadata) + '\r\n';
    body += `--${delimiter}\r\n`;
    body += 'Content-Type: application/json\r\n\r\n';
    body += data;
    body += close_delim;
    
    return body;
  }

  // تحديث واجهة المستخدم
  updateUI() {
    const googleLogin = document.getElementById('google-login');
    const googleProfile = document.getElementById('google-profile');
    const googleUserName = document.getElementById('google-user-name');
    const googleUserEmail = document.getElementById('google-user-email');
    const googleUserPhoto = document.getElementById('google-user-photo');
    
    if (this.isSignedIn && this.userInfo) {
      // إخفاء قسم تسجيل الدخول
      if (googleLogin) googleLogin.style.display = 'none';
      
      // عرض معلومات المستخدم
      if (googleProfile) googleProfile.style.display = 'block';
      if (googleUserName) googleUserName.textContent = this.userInfo.name || 'مستخدم Google';
      if (googleUserEmail) googleUserEmail.textContent = this.userInfo.email || '';
      if (googleUserPhoto && this.userInfo.picture) {
        googleUserPhoto.src = this.userInfo.picture;
        googleUserPhoto.style.display = 'block';
      }
      
    } else {
      // عرض قسم تسجيل الدخول
      if (googleLogin) googleLogin.style.display = 'block';
      if (googleProfile) googleProfile.style.display = 'none';
    }
  }

  // عرض رسالة للمستخدم
  showMessage(message, type = 'info') {
    // استخدام نظام الرسائل الموجود في التطبيق
    if (typeof showSuccessMessage === 'function' && type === 'success') {
      showSuccessMessage(message);
    } else if (typeof showErrorMessage === 'function' && type === 'error') {
      showErrorMessage(message);
    } else {
      console.log(`${type}: ${message}`);
    }
  }

  // الحصول على حالة تسجيل الدخول
  isUserSignedIn() {
    return this.isSignedIn;
  }

  // الحصول على معلومات المستخدم
  getUserData() {
    return this.userInfo;
  }

  // عرض تعليمات الإعداد
  showSetupInstructions() {
    const instructions = document.createElement('div');
    instructions.className = 'google-setup-instructions';
    instructions.innerHTML = `
      <div class="setup-modal">
        <div class="setup-content">
          <h3>🔧 إعداد تسجيل الدخول بـ Google</h3>
          <div class="error-details">
            <p><strong>المشكلة:</strong> إعدادات Google OAuth2 غير مكتملة أو غير صحيحة.</p>
          </div>

          <p>لاستخدام ميزة تسجيل الدخول بـ Google، يجب إعداد المشروع في Google Cloud Console:</p>

          <ol class="setup-steps">
            <li><strong>انتقل إلى:</strong> <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li><strong>أنشئ مشروع جديد</strong> أو اختر مشروع موجود</li>
            <li><strong>فعّل APIs:</strong>
              <ul>
                <li>Google+ API</li>
                <li>Google Drive API</li>
                <li>Google Identity API</li>
              </ul>
            </li>
            <li><strong>أنشئ OAuth 2.0 Client ID:</strong>
              <ul>
                <li>اختر "Chrome Extension" كنوع التطبيق</li>
                <li>أضف Extension ID: <code>${chrome.runtime.id}</code></li>
              </ul>
            </li>
            <li><strong>انسخ Client ID</strong> وضعه في manifest.json بدلاً من PLACEHOLDER</li>
            <li><strong>أعد تحميل الإضافة</strong> بعد التعديل</li>
          </ol>

          <div class="setup-note">
            <p><strong>ملاحظة مهمة:</strong> هذه الميزة تتطلب إعداد تقني من المطور. يمكنك استخدام جميع مميزات الإضافة الأخرى بدون تسجيل الدخول بـ Google.</p>
          </div>

          <div class="current-status">
            <p><strong>الحالة الحالية:</strong></p>
            <ul>
              <li>Extension ID: <code>${chrome.runtime.id}</code></li>
              <li>Client ID: <code>${chrome.runtime.getManifest().oauth2?.client_id || 'غير محدد'}</code></li>
              <li>الحالة: ${chrome.runtime.getManifest().oauth2?.client_id?.includes('PLACEHOLDER') ? '❌ يحتاج إعداد' : '✅ تم الإعداد'}</li>
            </ul>
          </div>

          <button class="close-setup-btn" id="close-setup-btn">إغلاق</button>
        </div>
      </div>
    `;

    document.body.appendChild(instructions);

    // إضافة event listener لزر الإغلاق
    setTimeout(() => {
      const closeBtn = document.getElementById('close-setup-btn');
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          instructions.remove();
        });
      }
    }, 100);

    // إزالة التعليمات بعد 30 ثانية
    setTimeout(() => {
      if (instructions.parentNode) {
        instructions.parentNode.removeChild(instructions);
      }
    }, 30000);
  }
}

// إنشاء مثيل عام
window.googleAuthManager = new GoogleAuthManager();
