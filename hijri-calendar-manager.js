// مدير التقويم الهجري المتقدم

class HijriCalendarManager {
  constructor() {
    this.islamicEvents = {
      // المناسبات الثابتة (حسب التاريخ الهجري)
      fixed: {
        '1-1': { name: 'رأس السنة الهجرية', type: 'celebration', importance: 'high' },
        '1-10': { name: 'يوم عاشوراء', type: 'religious', importance: 'high' },
        '3-12': { name: 'مولد النبي محمد ﷺ', type: 'celebration', importance: 'high' },
        '7-27': { name: 'ليلة الإسراء والمعراج', type: 'religious', importance: 'high' },
        '8-15': { name: 'ليلة النصف من شعبان', type: 'religious', importance: 'medium' },
        '9-1': { name: 'بداية شهر رمضان المبارك', type: 'ramadan', importance: 'high' },
        '9-21': { name: 'ليلة القدر (تقديرية)', type: 'ramadan', importance: 'high' },
        '9-23': { name: 'ليلة القدر (تقديرية)', type: 'ramadan', importance: 'high' },
        '9-25': { name: 'ليلة القدر (تقديرية)', type: 'ramadan', importance: 'high' },
        '9-27': { name: 'ليلة القدر (تقديرية)', type: 'ramadan', importance: 'high' },
        '9-29': { name: 'ليلة القدر (تقديرية)', type: 'ramadan', importance: 'high' },
        '10-1': { name: 'عيد الفطر المبارك', type: 'eid', importance: 'high' },
        '12-8': { name: 'بداية أيام الحج', type: 'hajj', importance: 'high' },
        '12-9': { name: 'يوم عرفة', type: 'hajj', importance: 'high' },
        '12-10': { name: 'عيد الأضحى المبارك', type: 'eid', importance: 'high' },
        '12-11': { name: 'أيام التشريق', type: 'hajj', importance: 'medium' },
        '12-12': { name: 'أيام التشريق', type: 'hajj', importance: 'medium' },
        '12-13': { name: 'أيام التشريق', type: 'hajj', importance: 'medium' }
      },
      
      // الأيام المستحبة (تتكرر شهرياً)
      monthly: {
        13: { name: 'يوم أبيض (13)', type: 'sunnah', importance: 'medium' },
        14: { name: 'يوم أبيض (14)', type: 'sunnah', importance: 'medium' },
        15: { name: 'يوم أبيض (15)', type: 'sunnah', importance: 'medium' }
      },
      
      // الأيام المستحبة (تتكرر أسبوعياً)
      weekly: {
        1: { name: 'يوم الاثنين (صيام مستحب)', type: 'sunnah', importance: 'low' },
        4: { name: 'يوم الخميس (صيام مستحب)', type: 'sunnah', importance: 'low' },
        5: { name: 'يوم الجمعة المبارك', type: 'religious', importance: 'high' }
      }
    };
    
    this.hijriMonths = [
      'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
      'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];
    
    this.weekDays = [
      'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
    ];
    
    this.settings = {
      adjustmentDays: 0, // تعديل يدوي للتاريخ الهجري
      showEvents: true,
      notifyEvents: true,
      notifyBeforeDays: 1
    };
    
    this.loadSettings();
  }

  // تحميل الإعدادات
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['hijriCalendarSettings']);
      if (result.hijriCalendarSettings) {
        this.settings = { ...this.settings, ...result.hijriCalendarSettings };
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات التقويم الهجري:', error);
    }
  }

  // حفظ الإعدادات
  async saveSettings() {
    try {
      await chrome.storage.local.set({ hijriCalendarSettings: this.settings });
    } catch (error) {
      console.error('خطأ في حفظ إعدادات التقويم الهجري:', error);
    }
  }

  // تحويل التاريخ الميلادي إلى هجري
  gregorianToHijri(gregorianDate) {
    const date = new Date(gregorianDate);
    
    // خوارزمية تحويل مبسطة ودقيقة
    const gYear = date.getFullYear();
    const gMonth = date.getMonth() + 1;
    const gDay = date.getDate();
    
    // حساب عدد الأيام منذ بداية التقويم الميلادي
    const gregorianDays = this.gregorianDateToDays(gYear, gMonth, gDay);
    
    // تاريخ بداية التقويم الهجري (16 يوليو 622م)
    const hijriEpochDays = this.gregorianDateToDays(622, 7, 16);
    
    // عدد الأيام منذ بداية التقويم الهجري
    const daysSinceHijriEpoch = gregorianDays - hijriEpochDays + this.settings.adjustmentDays;
    
    if (daysSinceHijriEpoch < 0) {
      return null; // تاريخ قبل بداية التقويم الهجري
    }
    
    // تحويل الأيام إلى تاريخ هجري
    return this.daysToHijriDate(daysSinceHijriEpoch);
  }

  // تحويل التاريخ الميلادي إلى عدد أيام
  gregorianDateToDays(year, month, day) {
    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;
    
    return day + Math.floor((153 * m + 2) / 5) + 365 * y + 
           Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400);
  }

  // تحويل عدد الأيام إلى تاريخ هجري
  daysToHijriDate(days) {
    // متوسط طول السنة الهجرية (354.367 يوم)
    const avgHijriYear = 354.367;
    
    // تقدير السنة الهجرية
    let hYear = Math.floor(days / avgHijriYear) + 1;
    
    // تعديل دقيق للسنة
    while (this.hijriYearToDays(hYear) > days) {
      hYear--;
    }
    while (this.hijriYearToDays(hYear + 1) <= days) {
      hYear++;
    }
    
    // حساب الأيام المتبقية في السنة
    const daysInYear = days - this.hijriYearToDays(hYear);
    
    // تحديد الشهر واليوم
    let hMonth = 1;
    let remainingDays = daysInYear;
    
    for (let month = 1; month <= 12; month++) {
      const daysInMonth = this.getHijriMonthLength(hYear, month);
      if (remainingDays < daysInMonth) {
        hMonth = month;
        break;
      }
      remainingDays -= daysInMonth;
      hMonth = month + 1;
    }
    
    const hDay = Math.floor(remainingDays) + 1;
    
    return {
      year: hYear,
      month: hMonth,
      day: hDay,
      monthName: this.hijriMonths[hMonth - 1],
      formatted: `${hDay} ${this.hijriMonths[hMonth - 1]} ${hYear} هـ`
    };
  }

  // حساب عدد الأيام منذ بداية التقويم الهجري للسنة المحددة
  hijriYearToDays(hYear) {
    let days = 0;
    for (let year = 1; year < hYear; year++) {
      days += this.isHijriLeapYear(year) ? 355 : 354;
    }
    return days;
  }

  // تحديد ما إذا كانت السنة الهجرية كبيسة
  isHijriLeapYear(hYear) {
    // دورة 30 سنة: السنوات 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29 كبيسة
    const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
    const yearInCycle = hYear % 30;
    return leapYears.includes(yearInCycle);
  }

  // حساب عدد أيام الشهر الهجري
  getHijriMonthLength(hYear, hMonth) {
    // الأشهر الفردية 30 يوم، الزوجية 29 يوم
    // إلا ذو الحجة في السنة الكبيسة يكون 30 يوم
    if (hMonth % 2 === 1) {
      return 30; // شهر فردي
    } else if (hMonth === 12 && this.isHijriLeapYear(hYear)) {
      return 30; // ذو الحجة في سنة كبيسة
    } else {
      return 29; // شهر زوجي عادي
    }
  }

  // الحصول على التاريخ الهجري الحالي
  getCurrentHijriDate() {
    return this.gregorianToHijri(new Date());
  }

  // الحصول على المناسبات في تاريخ معين
  getEventsForDate(hijriDate) {
    const events = [];
    const dateKey = `${hijriDate.month}-${hijriDate.day}`;
    
    // المناسبات الثابتة
    if (this.islamicEvents.fixed[dateKey]) {
      events.push({
        ...this.islamicEvents.fixed[dateKey],
        date: hijriDate,
        category: 'fixed'
      });
    }
    
    // الأيام المستحبة الشهرية
    if (this.islamicEvents.monthly[hijriDate.day]) {
      events.push({
        ...this.islamicEvents.monthly[hijriDate.day],
        date: hijriDate,
        category: 'monthly'
      });
    }
    
    // الأيام المستحبة الأسبوعية
    const gregorianDate = new Date();
    const weekDay = gregorianDate.getDay();
    if (this.islamicEvents.weekly[weekDay]) {
      events.push({
        ...this.islamicEvents.weekly[weekDay],
        date: hijriDate,
        category: 'weekly'
      });
    }
    
    return events;
  }

  // الحصول على المناسبات القادمة
  getUpcomingEvents(daysAhead = 30) {
    const events = [];
    const today = new Date();
    
    for (let i = 0; i <= daysAhead; i++) {
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + i);
      
      const hijriDate = this.gregorianToHijri(futureDate);
      if (hijriDate) {
        const dayEvents = this.getEventsForDate(hijriDate);
        dayEvents.forEach(event => {
          events.push({
            ...event,
            gregorianDate: new Date(futureDate),
            daysFromNow: i
          });
        });
      }
    }
    
    // ترتيب حسب الأهمية والتاريخ
    return events.sort((a, b) => {
      const importanceOrder = { high: 3, medium: 2, low: 1 };
      const importanceDiff = importanceOrder[b.importance] - importanceOrder[a.importance];
      
      if (importanceDiff !== 0) return importanceDiff;
      return a.daysFromNow - b.daysFromNow;
    });
  }

  // إعداد تذكيرات المناسبات
  async setupEventReminders() {
    if (!this.settings.notifyEvents) return;

    // إزالة التذكيرات القديمة
    await this.clearEventAlarms();

    const upcomingEvents = this.getUpcomingEvents(30);
    
    for (const event of upcomingEvents) {
      if (event.importance === 'high' && event.daysFromNow <= 7) {
        // تذكير قبل المناسبة
        const reminderDate = new Date(event.gregorianDate);
        reminderDate.setDate(reminderDate.getDate() - this.settings.notifyBeforeDays);
        
        if (reminderDate > new Date()) {
          chrome.alarms.create(`islamic-event-${event.name}`, {
            when: reminderDate.getTime()
          });
        }
        
        // تذكير يوم المناسبة
        if (event.gregorianDate > new Date()) {
          chrome.alarms.create(`islamic-event-today-${event.name}`, {
            when: event.gregorianDate.getTime()
          });
        }
      }
    }
  }

  // إزالة تذكيرات المناسبات
  async clearEventAlarms() {
    const alarms = await chrome.alarms.getAll();
    for (const alarm of alarms) {
      if (alarm.name.startsWith('islamic-event-')) {
        chrome.alarms.clear(alarm.name);
      }
    }
  }

  // تعديل التاريخ الهجري يدوياً
  adjustHijriDate(days) {
    this.settings.adjustmentDays = days;
    this.saveSettings();
  }

  // الحصول على معلومات الشهر الهجري الحالي
  getCurrentMonthInfo() {
    const hijriDate = this.getCurrentHijriDate();
    if (!hijriDate) return null;

    const monthLength = this.getHijriMonthLength(hijriDate.year, hijriDate.month);
    const isRamadan = hijriDate.month === 9;
    const isHajjMonth = hijriDate.month === 12;
    
    return {
      ...hijriDate,
      monthLength,
      isRamadan,
      isHajjMonth,
      daysRemaining: monthLength - hijriDate.day,
      progress: (hijriDate.day / monthLength) * 100
    };
  }

  // تحديد ما إذا كان اليوم مناسبة خاصة
  isTodaySpecial() {
    const hijriDate = this.getCurrentHijriDate();
    if (!hijriDate) return false;

    const events = this.getEventsForDate(hijriDate);
    return events.some(event => event.importance === 'high');
  }

  // الحصول على نصائح وأعمال مستحبة لليوم
  getTodayRecommendations() {
    const hijriDate = this.getCurrentHijriDate();
    if (!hijriDate) return [];

    const events = this.getEventsForDate(hijriDate);
    const recommendations = [];

    events.forEach(event => {
      switch (event.type) {
        case 'ramadan':
          recommendations.push('الإكثار من تلاوة القرآن والدعاء');
          recommendations.push('الاعتكاف في المسجد إن أمكن');
          break;
        case 'hajj':
          recommendations.push('الإكثار من التكبير والتهليل');
          recommendations.push('صيام يوم عرفة لغير الحاج');
          break;
        case 'sunnah':
          recommendations.push('صيام هذا اليوم مستحب');
          recommendations.push('الإكثار من الذكر والاستغفار');
          break;
        case 'religious':
          recommendations.push('الإكثار من الصلاة على النبي ﷺ');
          recommendations.push('قراءة سورة الكهف (يوم الجمعة)');
          break;
      }
    });

    return [...new Set(recommendations)]; // إزالة التكرار
  }
}

// إنشاء مثيل عام لمدير التقويم الهجري
const hijriCalendarManager = new HijriCalendarManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.hijriCalendarManager = hijriCalendarManager;
}
