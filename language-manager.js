// مدير اللغات المتعددة

class LanguageManager {
  constructor() {
    this.currentLanguage = 'ar'; // العربية افتراضياً
    this.supportedLanguages = ['ar', 'en', 'fr', 'ur'];
    this.translations = {};
    this.loadTranslations();
    this.loadSettings();
  }

  // تحميل الترجمات
  loadTranslations() {
    this.translations = {
      // العربية (الافتراضية)
      ar: {
        // واجهة عامة
        'app_title': 'أذكار المسلم',
        'welcome_message': 'بسم الله الرحمن الرحيم',
        'welcome_subtitle': 'مرحباً بك في تطبيق أذكار المسلم',
        'current_time': 'الوقت الحالي',
        'next_azkar': 'الذكر القادم',
        
        // التبويبات
        'tab_main': 'الرئيسية',
        'tab_morning': 'أذكار الصباح',
        'tab_evening': 'أذكار المساء',
        'tab_prayer': 'بعد الصلاة',
        'tab_ramadan': '🌙 رمضان',
        'tab_achievements': '🏆 الإنجازات',
        'tab_settings': 'الإعدادات',
        
        // أوقات الصلاة
        'prayer_times': '🕌 أوقات الصلاة',
        'next_prayer': 'الصلاة القادمة',
        'hijri_date': 'التاريخ الهجري',
        'fajr': 'الفجر',
        'dhuhr': 'الظهر',
        'asr': 'العصر',
        'maghrib': 'المغرب',
        'isha': 'العشاء',
        
        // المناسبات
        'islamic_events': '🌟 المناسبات الإسلامية',
        'today': 'اليوم',
        'tomorrow': 'غداً',
        'in_days': 'خلال {0} أيام',
        
        // رمضان
        'ramadan_blessed': 'رمضان المبارك',
        'days_remaining': 'الأيام المتبقية',
        'iftar_azkar': '🌅 أذكار الإفطار',
        'suhoor_azkar': '🌄 أذكار السحور',
        'laylat_qadr': '✨ أذكار ليلة القدر',
        'last_ten_nights': '🕌 أذكار العشر الأواخر',
        'today_tips': '💡 نصائح اليوم',
        'ramadan_stats': '📊 إحصائياتك في رمضان',
        
        // الإنجازات
        'achievements_title': '🏆 الإنجازات والتحفيز',
        'level': 'المستوى',
        'points': 'نقطة',
        'your_stats': '📊 إحصائياتك',
        'total_reads': 'إجمالي القراءات',
        'current_streak': 'الأيام المتتالية',
        'total_points': 'إجمالي النقاط',
        'achievements_count': 'الإنجازات المحققة',
        'active_challenges': '🎯 التحديات النشطة',
        'achievements_gallery': '🏆 معرض الإنجازات',
        'recent_achievements': '🌟 الإنجازات الحديثة',
        
        // الإعدادات
        'azkar_settings': '⏰ إعدادات الأذكار',
        'morning_time': 'وقت أذكار الصباح',
        'evening_time': 'وقت أذكار المساء',
        'after_prayer': 'تفعيل أذكار بعد الصلاة',
        'theme_selection': '🎨 اختيار الثيم',
        'prayer_settings': '🕌 إعدادات أوقات الصلاة',
        'auto_location': 'تحديد الموقع تلقائياً',
        'manual_city': 'المدينة (يدوي)',
        'prayer_notifications': 'تفعيل إشعارات الصلاة',
        'reminder_minutes': 'التذكير قبل الأذان (بالدقائق)',
        'save_settings': 'حفظ الإعدادات',
        'export_settings': 'تصدير الإعدادات',
        'import_settings': 'استيراد الإعدادات',
        
        // أزرار وعناصر تحكم
        'play': 'تشغيل',
        'pause': 'إيقاف مؤقت',
        'stop': 'إيقاف',
        'repeat': 'تكرار',
        'volume': 'مستوى الصوت',
        'speed': 'السرعة',
        'reader': 'القارئ',
        'loading': 'جاري التحميل...',
        'no_audio': 'لا يوجد ملف صوتي',
        
        // رسائل
        'location_permission': 'يرجى السماح بالوصول للموقع الجغرافي',
        'allow_location': 'السماح بالوصول للموقع',
        'audio_error': 'خطأ في تشغيل الملف الصوتي',
        'settings_saved': 'تم حفظ الإعدادات بنجاح',
        'settings_imported': 'تم استيراد الإعدادات بنجاح'
      },
      
      // الإنجليزية
      en: {
        'app_title': 'Muslim Azkar',
        'welcome_message': 'In the name of Allah, the Most Gracious, the Most Merciful',
        'welcome_subtitle': 'Welcome to Muslim Azkar App',
        'current_time': 'Current Time',
        'next_azkar': 'Next Azkar',
        
        'tab_main': 'Home',
        'tab_morning': 'Morning Azkar',
        'tab_evening': 'Evening Azkar',
        'tab_prayer': 'After Prayer',
        'tab_ramadan': '🌙 Ramadan',
        'tab_achievements': '🏆 Achievements',
        'tab_settings': 'Settings',
        
        'prayer_times': '🕌 Prayer Times',
        'next_prayer': 'Next Prayer',
        'hijri_date': 'Hijri Date',
        'fajr': 'Fajr',
        'dhuhr': 'Dhuhr',
        'asr': 'Asr',
        'maghrib': 'Maghrib',
        'isha': 'Isha',
        
        'islamic_events': '🌟 Islamic Events',
        'today': 'Today',
        'tomorrow': 'Tomorrow',
        'in_days': 'In {0} days',
        
        'ramadan_blessed': 'Blessed Ramadan',
        'days_remaining': 'Days Remaining',
        'iftar_azkar': '🌅 Iftar Azkar',
        'suhoor_azkar': '🌄 Suhoor Azkar',
        'laylat_qadr': '✨ Laylat al-Qadr Azkar',
        'last_ten_nights': '🕌 Last Ten Nights Azkar',
        'today_tips': '💡 Today\'s Tips',
        'ramadan_stats': '📊 Your Ramadan Stats',
        
        'achievements_title': '🏆 Achievements & Motivation',
        'level': 'Level',
        'points': 'Points',
        'your_stats': '📊 Your Statistics',
        'total_reads': 'Total Reads',
        'current_streak': 'Current Streak',
        'total_points': 'Total Points',
        'achievements_count': 'Achievements Unlocked',
        'active_challenges': '🎯 Active Challenges',
        'achievements_gallery': '🏆 Achievements Gallery',
        'recent_achievements': '🌟 Recent Achievements',
        
        'azkar_settings': '⏰ Azkar Settings',
        'morning_time': 'Morning Azkar Time',
        'evening_time': 'Evening Azkar Time',
        'after_prayer': 'Enable After Prayer Azkar',
        'theme_selection': '🎨 Theme Selection',
        'prayer_settings': '🕌 Prayer Times Settings',
        'auto_location': 'Auto-detect Location',
        'manual_city': 'City (Manual)',
        'prayer_notifications': 'Enable Prayer Notifications',
        'reminder_minutes': 'Reminder Before Adhan (Minutes)',
        'save_settings': 'Save Settings',
        'export_settings': 'Export Settings',
        'import_settings': 'Import Settings',
        
        'play': 'Play',
        'pause': 'Pause',
        'stop': 'Stop',
        'repeat': 'Repeat',
        'volume': 'Volume',
        'speed': 'Speed',
        'reader': 'Reader',
        'loading': 'Loading...',
        'no_audio': 'No audio file',
        
        'location_permission': 'Please allow access to your location',
        'allow_location': 'Allow Location Access',
        'audio_error': 'Error playing audio file',
        'settings_saved': 'Settings saved successfully',
        'settings_imported': 'Settings imported successfully'
      },
      
      // الفرنسية
      fr: {
        'app_title': 'Azkar du Musulman',
        'welcome_message': 'Au nom d\'Allah, le Tout Miséricordieux, le Très Miséricordieux',
        'welcome_subtitle': 'Bienvenue dans l\'application Azkar du Musulman',
        'current_time': 'Heure Actuelle',
        'next_azkar': 'Prochain Azkar',
        
        'tab_main': 'Accueil',
        'tab_morning': 'Azkar du Matin',
        'tab_evening': 'Azkar du Soir',
        'tab_prayer': 'Après la Prière',
        'tab_ramadan': '🌙 Ramadan',
        'tab_achievements': '🏆 Réalisations',
        'tab_settings': 'Paramètres',
        
        'prayer_times': '🕌 Heures de Prière',
        'next_prayer': 'Prochaine Prière',
        'hijri_date': 'Date Hijri',
        'fajr': 'Fajr',
        'dhuhr': 'Dhuhr',
        'asr': 'Asr',
        'maghrib': 'Maghrib',
        'isha': 'Isha',
        
        'islamic_events': '🌟 Événements Islamiques',
        'today': 'Aujourd\'hui',
        'tomorrow': 'Demain',
        'in_days': 'Dans {0} jours',
        
        'ramadan_blessed': 'Ramadan Béni',
        'days_remaining': 'Jours Restants',
        'iftar_azkar': '🌅 Azkar d\'Iftar',
        'suhoor_azkar': '🌄 Azkar de Suhoor',
        'laylat_qadr': '✨ Azkar de Laylat al-Qadr',
        'last_ten_nights': '🕌 Azkar des Dix Dernières Nuits',
        'today_tips': '💡 Conseils du Jour',
        'ramadan_stats': '📊 Vos Statistiques Ramadan',
        
        'achievements_title': '🏆 Réalisations et Motivation',
        'level': 'Niveau',
        'points': 'Points',
        'your_stats': '📊 Vos Statistiques',
        'total_reads': 'Total des Lectures',
        'current_streak': 'Série Actuelle',
        'total_points': 'Total des Points',
        'achievements_count': 'Réalisations Débloquées',
        'active_challenges': '🎯 Défis Actifs',
        'achievements_gallery': '🏆 Galerie des Réalisations',
        'recent_achievements': '🌟 Réalisations Récentes',
        
        'azkar_settings': '⏰ Paramètres Azkar',
        'morning_time': 'Heure Azkar du Matin',
        'evening_time': 'Heure Azkar du Soir',
        'after_prayer': 'Activer Azkar Après Prière',
        'theme_selection': '🎨 Sélection du Thème',
        'prayer_settings': '🕌 Paramètres Heures de Prière',
        'auto_location': 'Détecter Automatiquement la Position',
        'manual_city': 'Ville (Manuel)',
        'prayer_notifications': 'Activer Notifications de Prière',
        'reminder_minutes': 'Rappel Avant Adhan (Minutes)',
        'save_settings': 'Sauvegarder Paramètres',
        'export_settings': 'Exporter Paramètres',
        'import_settings': 'Importer Paramètres',
        
        'play': 'Jouer',
        'pause': 'Pause',
        'stop': 'Arrêter',
        'repeat': 'Répéter',
        'volume': 'Volume',
        'speed': 'Vitesse',
        'reader': 'Lecteur',
        'loading': 'Chargement...',
        'no_audio': 'Pas de fichier audio',
        
        'location_permission': 'Veuillez autoriser l\'accès à votre position',
        'allow_location': 'Autoriser l\'Accès à la Position',
        'audio_error': 'Erreur lors de la lecture du fichier audio',
        'settings_saved': 'Paramètres sauvegardés avec succès',
        'settings_imported': 'Paramètres importés avec succès'
      },
      
      // الأردية
      ur: {
        'app_title': 'مسلم اذکار',
        'welcome_message': 'بسم اللہ الرحمن الرحیم',
        'welcome_subtitle': 'مسلم اذکار ایپ میں خوش آمدید',
        'current_time': 'موجودہ وقت',
        'next_azkar': 'اگلا ذکر',
        
        'tab_main': 'ہوم',
        'tab_morning': 'صبح کے اذکار',
        'tab_evening': 'شام کے اذکار',
        'tab_prayer': 'نماز کے بعد',
        'tab_ramadan': '🌙 رمضان',
        'tab_achievements': '🏆 کامیابیاں',
        'tab_settings': 'سیٹنگز',
        
        'prayer_times': '🕌 نماز کے اوقات',
        'next_prayer': 'اگلی نماز',
        'hijri_date': 'ہجری تاریخ',
        'fajr': 'فجر',
        'dhuhr': 'ظہر',
        'asr': 'عصر',
        'maghrib': 'مغرب',
        'isha': 'عشاء',
        
        'islamic_events': '🌟 اسلامی واقعات',
        'today': 'آج',
        'tomorrow': 'کل',
        'in_days': '{0} دنوں میں',
        
        'ramadan_blessed': 'رمضان مبارک',
        'days_remaining': 'باقی دن',
        'iftar_azkar': '🌅 افطار کے اذکار',
        'suhoor_azkar': '🌄 سحری کے اذکار',
        'laylat_qadr': '✨ شب قدر کے اذکار',
        'last_ten_nights': '🕌 آخری عشرے کے اذکار',
        'today_tips': '💡 آج کے مشورے',
        'ramadan_stats': '📊 آپ کے رمضان کے اعداد و شمار',
        
        'achievements_title': '🏆 کامیابیاں اور حوصلہ افزائی',
        'level': 'لیول',
        'points': 'پوائنٹس',
        'your_stats': '📊 آپ کے اعداد و شمار',
        'total_reads': 'کل پڑھائی',
        'current_streak': 'موجودہ سلسلہ',
        'total_points': 'کل پوائنٹس',
        'achievements_count': 'حاصل شدہ کامیابیاں',
        'active_challenges': '🎯 فعال چیلنجز',
        'achievements_gallery': '🏆 کامیابیوں کی گیلری',
        'recent_achievements': '🌟 حالیہ کامیابیاں',
        
        'azkar_settings': '⏰ اذکار کی سیٹنگز',
        'morning_time': 'صبح کے اذکار کا وقت',
        'evening_time': 'شام کے اذکار کا وقت',
        'after_prayer': 'نماز کے بعد اذکار فعال کریں',
        'theme_selection': '🎨 تھیم کا انتخاب',
        'prayer_settings': '🕌 نماز کے اوقات کی سیٹنگز',
        'auto_location': 'خودکار مقام کا تعین',
        'manual_city': 'شہر (دستی)',
        'prayer_notifications': 'نماز کی اطلاعات فعال کریں',
        'reminder_minutes': 'اذان سے پہلے یاد دہانی (منٹ)',
        'save_settings': 'سیٹنگز محفوظ کریں',
        'export_settings': 'سیٹنگز ایکسپورٹ کریں',
        'import_settings': 'سیٹنگز امپورٹ کریں',
        
        'play': 'چلائیں',
        'pause': 'رک جائیں',
        'stop': 'بند کریں',
        'repeat': 'دہرائیں',
        'volume': 'آواز',
        'speed': 'رفتار',
        'reader': 'قاری',
        'loading': 'لوڈ ہو رہا ہے...',
        'no_audio': 'کوئی آڈیو فائل نہیں',
        
        'location_permission': 'براہ کرم اپنے مقام تک رسائی کی اجازت دیں',
        'allow_location': 'مقام کی رسائی کی اجازت دیں',
        'audio_error': 'آڈیو فائل چلانے میں خرابی',
        'settings_saved': 'سیٹنگز کامیابی سے محفوظ ہو گئیں',
        'settings_imported': 'سیٹنگز کامیابی سے امپورٹ ہو گئیں'
      }
    };
  }

  // تحميل إعدادات اللغة
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['currentLanguage']);
      if (result.currentLanguage && this.supportedLanguages.includes(result.currentLanguage)) {
        this.currentLanguage = result.currentLanguage;
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات اللغة:', error);
    }
  }

  // حفظ إعدادات اللغة
  async saveSettings() {
    try {
      await chrome.storage.local.set({ currentLanguage: this.currentLanguage });
    } catch (error) {
      console.error('خطأ في حفظ إعدادات اللغة:', error);
    }
  }

  // تغيير اللغة
  async changeLanguage(languageCode) {
    if (this.supportedLanguages.includes(languageCode)) {
      this.currentLanguage = languageCode;
      await this.saveSettings();
      this.updatePageLanguage();
      return true;
    }
    return false;
  }

  // الحصول على ترجمة
  translate(key, ...args) {
    const translation = this.translations[this.currentLanguage]?.[key] || 
                       this.translations['ar'][key] || 
                       key;
    
    // استبدال المتغيرات {0}, {1}, إلخ
    return args.reduce((text, arg, index) => {
      return text.replace(`{${index}}`, arg);
    }, translation);
  }

  // تحديث لغة الصفحة
  updatePageLanguage() {
    // تحديث اتجاه النص
    document.documentElement.dir = (this.currentLanguage === 'ar' || this.currentLanguage === 'ur') ? 'rtl' : 'ltr';
    document.documentElement.lang = this.currentLanguage;
    
    // تحديث جميع العناصر المترجمة
    document.querySelectorAll('[data-translate]').forEach(element => {
      const key = element.getAttribute('data-translate');
      element.textContent = this.translate(key);
    });
    
    // تحديث العناصر ذات الترجمة في placeholder
    document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
      const key = element.getAttribute('data-translate-placeholder');
      element.placeholder = this.translate(key);
    });
    
    // تحديث العناصر ذات الترجمة في title
    document.querySelectorAll('[data-translate-title]').forEach(element => {
      const key = element.getAttribute('data-translate-title');
      element.title = this.translate(key);
    });
  }

  // الحصول على اللغات المدعومة
  getSupportedLanguages() {
    return [
      { code: 'ar', name: 'العربية', nativeName: 'العربية' },
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'fr', name: 'Français', nativeName: 'Français' },
      { code: 'ur', name: 'Urdu', nativeName: 'اردو' }
    ];
  }

  // الحصول على اللغة الحالية
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  // التحقق من اتجاه النص
  isRTL() {
    return this.currentLanguage === 'ar' || this.currentLanguage === 'ur';
  }
}

// إنشاء مثيل عام لمدير اللغات
const languageManager = new LanguageManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.languageManager = languageManager;
}
