// متغيرات الصوت العامة
let currentAudio = null;
let audioEnabled = true;

document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإعدادات
  loadSettings();

  // عرض الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);

  // عرض معلومات الذكر القادم
  updateNextAzkar();

  // تحميل الأذكار
  loadAzkar();

  // إعداد التبويبات
  setupTabs();

  // إعداد زر حفظ الإعدادات
  document.getElementById('save-settings').addEventListener('click', saveSettings);

  // تحميل إعدادات الصوت
  loadAudioSettings();

  // تهيئة المميزات المحسنة
  if (typeof initEnhancedFeatures === 'function') {
    initEnhancedFeatures();
  }

  // تهيئة أوقات الصلاة
  initPrayerTimes();

  // تهيئة اختيار الثيمات
  initThemeSelector();

  // تهيئة التقويم الهجري
  initHijriCalendar();

  // تهيئة أذكار رمضان
  initRamadanAzkar();

  // تهيئة نظام الإنجازات
  initAchievements();

  // تهيئة دعم اللغات
  initLanguageSupport();

  // تهيئة النسخ الاحتياطي
  initBackupSystem();

  // تهيئة نظام الملف الشخصي
  initProfileSystem();

  // تهيئة تسجيل الدخول بـ Google
  initGoogleAuth();

  // تحميل عداد الأذكار اليومي
  loadDailyCounter();

  // إعداد إعادة تعيين العداد في منتصف الليل
  setupMidnightReset();

  // تهيئة التأثيرات البصرية
  if (window.visualEffectsManager) {
    window.visualEffectsManager.init();
  }

  // تهيئة نظام التذكيرات الذكية
  if (window.smartRemindersManager) {
    window.smartRemindersManager.init();
  }

  // تهيئة مدير الإحصائيات المفصلة
  if (window.detailedStatisticsManager) {
    window.detailedStatisticsManager.init();
  }

  // تهيئة نظام النقاط المحسن
  if (window.enhancedPointsSystem) {
    window.enhancedPointsSystem.init();
    window.enhancedPointsSystem.addPointsStyles();
  }

  // تهيئة تحسينات واجهة المستخدم
  if (window.uiEnhancements) {
    window.uiEnhancements.init();
  }

  // إضافة تأثيرات بصرية للعناصر
  setTimeout(() => {
    document.querySelectorAll('.zikr-card').forEach((card, index) => {
      if (window.uiEnhancements) {
        window.uiEnhancements.slideIn(card, index * 100);
      }
    });
  }, 500);
});

// تحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('ar-SA');
  document.getElementById('current-time').textContent = timeString;
}

// التحقق من صحة الوقت
function validateTime(timeString) {
  if (!timeString || typeof timeString !== 'string') {
    return false;
  }

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(timeString)) {
    return false;
  }

  const [hours, minutes] = timeString.split(':').map(Number);
  return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}

// تحميل الإعدادات
function loadSettings() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00",
      afterPrayer: true
    };
    
    document.getElementById('morning-time').value = times.morning;
    document.getElementById('evening-time').value = times.evening;
    document.getElementById('after-prayer').checked = times.afterPrayer;
  });
}

// حفظ الإعدادات
function saveSettings() {
  try {
    const morningTimeElement = document.getElementById('morning-time');
    const eveningTimeElement = document.getElementById('evening-time');
    const afterPrayerElement = document.getElementById('after-prayer');

    if (!morningTimeElement || !eveningTimeElement || !afterPrayerElement) {
      alert('خطأ: لا يمكن العثور على عناصر الإعدادات');
      return;
    }

    const morningTime = morningTimeElement.value;
    const eveningTime = eveningTimeElement.value;
    const afterPrayer = afterPrayerElement.checked;

    // التحقق من صحة الأوقات
    if (!morningTime || !eveningTime) {
      alert('يرجى تحديد أوقات صحيحة للأذكار');
      return;
    }

    // التحقق من صحة تنسيق الوقت
    if (!validateTime(morningTime) || !validateTime(eveningTime)) {
      alert('يرجى إدخال أوقات صحيحة (00:00 - 23:59)');
      return;
    }

    // التحقق من أن وقت المساء مختلف عن وقت الصباح
    if (morningTime === eveningTime) {
      alert('يجب أن يكون وقت أذكار المساء مختلفاً عن وقت أذكار الصباح');
      return;
    }

    // إعدادات الصوت
    const audioEnabledElement = document.getElementById('audio-enabled');
    const audioVolumeElement = document.getElementById('audio-volume');

    const settings = {
      morning: morningTime,
      evening: eveningTime,
      afterPrayer: afterPrayer
    };

    const audioSettings = {
      enabled: audioEnabledElement ? audioEnabledElement.checked : true,
      volume: audioVolumeElement ? parseFloat(audioVolumeElement.value) : 0.7
    };

    // تحديث المتغير العام
    audioEnabled = audioSettings.enabled;

    // حفظ الإعدادات
    chrome.storage.local.set({
      azkarTimes: settings,
      audioSettings: audioSettings
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في حفظ الإعدادات:', chrome.runtime.lastError);
        alert('حدث خطأ في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      } else {
        alert('تم حفظ الإعدادات بنجاح');
        updateNextAzkar();
      }
    });
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
  }
}

// تحديث معلومات الذكر القادم
function updateNextAzkar() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00"
    };
    
    const now = new Date();
    
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    
    let morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    
    let eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    
    // إذا كان الوقت الحالي بعد وقت الصباح، نضبط وقت الصباح ليوم غد
    if (now > morningTime) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    // إذا كان الوقت الحالي بعد وقت المساء، نضبط وقت المساء ليوم غد
    if (now > eveningTime) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    // تحديد الذكر القادم (الأقرب زمنياً)
    let nextAzkarType, nextAzkarTime;
    
    if (morningTime < eveningTime) {
      nextAzkarType = "أذكار الصباح";
      nextAzkarTime = morningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      nextAzkarType = "أذكار المساء";
      nextAzkarTime = eveningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    }
    
    document.getElementById('next-azkar-type').textContent = nextAzkarType;
    document.getElementById('next-azkar-time').textContent = nextAzkarTime;
  });
}

// تحميل الأذكار مع تحسينات الأداء
function loadAzkar() {
  try {
    // استخدام requestAnimationFrame لتحسين الأداء
    requestAnimationFrame(() => {
      loadAzkarCategory('morning-azkar', morningAzkar, 'morning', 'أذكار الصباح');
    });

    requestAnimationFrame(() => {
      loadAzkarCategory('evening-azkar', eveningAzkar, 'evening', 'أذكار المساء');
    });

    requestAnimationFrame(() => {
      loadAzkarCategory('prayer-azkar', afterPrayerAzkar, 'prayer', 'أذكار بعد الصلاة');
    });

  } catch (error) {
    console.error('خطأ في تحميل الأذكار:', error);
    showMessage('حدث خطأ في تحميل الأذكار. يرجى إعادة تحميل الإضافة.', 'error');
  }
}

// تحميل فئة أذكار محددة مع تحسين الأداء
function loadAzkarCategory(containerId, azkarArray, category, categoryName) {
  const container = document.getElementById(containerId);
  if (!container) return;

  if (typeof azkarArray !== 'undefined' && Array.isArray(azkarArray)) {
    // استخدام DocumentFragment لتحسين الأداء
    const fragment = document.createDocumentFragment();

    // تحميل الأذكار على دفعات لتجنب تجميد الواجهة
    const batchSize = 5;
    let currentIndex = 0;

    function loadBatch() {
      const endIndex = Math.min(currentIndex + batchSize, azkarArray.length);

      for (let i = currentIndex; i < endIndex; i++) {
        const zikrElement = createZikrElement(azkarArray[i], category);
        fragment.appendChild(zikrElement);
      }

      currentIndex = endIndex;

      if (currentIndex < azkarArray.length) {
        // تحميل الدفعة التالية في الإطار التالي
        requestAnimationFrame(loadBatch);
      } else {
        // إضافة جميع العناصر دفعة واحدة
        container.appendChild(fragment);

        // تحميل حالات القراءة بعد إنشاء العناصر
        loadReadStatusForCategory(category);
      }
    }

    loadBatch();
  } else {
    container.innerHTML = `<p class="error-message">فشل في تحميل ${categoryName}. يرجى إعادة تحميل الإضافة.</p>`;
  }
}

// تحميل حالات القراءة لفئة معينة
function loadReadStatusForCategory(category) {
  chrome.storage.local.get(['readAzkar', 'zikrProgress'], () => {
    // البحث عن جميع الأذكار في هذه الفئة
    const zikrElements = document.querySelectorAll(`[data-zikr-id^="${category}_"]`);

    zikrElements.forEach(zikrElement => {
      const zikrId = zikrElement.getAttribute('data-zikr-id');
      const readButton = zikrElement.querySelector('.read-button');

      if (readButton) {
        checkReadStatus(zikrId, zikrElement, readButton);
      }
    });
  });
}

// إنشاء عنصر ذكر
function createZikrElement(zikr, category = 'morning') {
  const zikrElement = document.createElement('div');
  zikrElement.className = 'zikr-item';

  // إضافة أزرار التحكم في الصوت
  const audioControls = document.createElement('div');
  audioControls.className = 'audio-controls';

  const audioPath = getAudioPath(zikr.text, category);
  if (audioPath) {
    const playButton = document.createElement('button');
    playButton.className = 'audio-btn play-btn';
    playButton.innerHTML = '🔊';
    playButton.title = 'تشغيل التلاوة';
    playButton.addEventListener('click', () => playAudio(audioPath));

    const stopButton = document.createElement('button');
    stopButton.className = 'audio-btn stop-btn';
    stopButton.innerHTML = '⏹️';
    stopButton.title = 'إيقاف التلاوة';
    stopButton.addEventListener('click', () => stopAudio());

    audioControls.appendChild(playButton);
    audioControls.appendChild(stopButton);
  }

  const zikrText = document.createElement('div');
  zikrText.className = 'zikr-text';
  zikrText.textContent = zikr.text;

  const zikrTranslation = document.createElement('div');
  zikrTranslation.className = 'zikr-translation';
  zikrTranslation.textContent = zikr.translation || '';

  const zikrCountContainer = document.createElement('div');
  zikrCountContainer.className = 'zikr-count-container';

  const zikrCount = document.createElement('div');
  zikrCount.className = 'zikr-count';
  zikrCount.textContent = `عدد المرات: ${zikr.count}`;

  // إضافة عداد تفاعلي إذا كان العدد أكثر من 1
  if (zikr.count > 1) {
    const counterContainer = document.createElement('div');
    counterContainer.className = 'counter-container';

    const counterDisplay = document.createElement('span');
    counterDisplay.className = 'counter-display';
    counterDisplay.textContent = `0 / ${zikr.count}`;

    const incrementButton = document.createElement('button');
    incrementButton.className = 'counter-btn';
    incrementButton.textContent = '+';
    incrementButton.addEventListener('click', () => incrementCounter(counterDisplay, zikr.count));

    const resetButton = document.createElement('button');
    resetButton.className = 'counter-btn reset-btn';
    resetButton.textContent = 'إعادة تعيين';
    resetButton.addEventListener('click', () => resetCounter(counterDisplay, zikr.count));

    counterContainer.appendChild(counterDisplay);
    counterContainer.appendChild(incrementButton);
    counterContainer.appendChild(resetButton);
    zikrCountContainer.appendChild(counterContainer);
  }

  // إضافة زر المشاركة
  const shareButton = document.createElement('button');
  shareButton.className = 'share-button';
  shareButton.innerHTML = '📤';
  shareButton.title = 'مشاركة الذكر';
  shareButton.addEventListener('click', () => shareZikr(zikr.text, zikr.translation || ''));

  // إضافة زر "تم القراءة"
  const readButton = document.createElement('button');
  readButton.className = 'read-button';
  readButton.innerHTML = '✅';
  readButton.title = 'تم القراءة';

  // إنشاء معرف فريد للذكر
  const zikrId = `${category}_${zikr.text.substring(0, 30).replace(/\s+/g, '_').replace(/[^\w\u0600-\u06FF]/g, '')}`;
  zikrElement.setAttribute('data-zikr-id', zikrId);
  zikrElement.setAttribute('data-zikr-count', zikr.count);

  // إضافة عداد للأذكار المتكررة
  if (zikr.count > 1) {
    const readCounter = document.createElement('div');
    readCounter.className = 'read-counter';
    readCounter.innerHTML = `<span class="read-progress">0/${zikr.count}</span>`;
    readButton.appendChild(readCounter);
    readButton.title = `اقرأ ${zikr.count} مرات`;


  }

  readButton.addEventListener('click', () => markAsRead(zikrId, zikrElement, readButton, zikr.count));

  // إضافة حاوي للأزرار
  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'button-container';
  buttonContainer.appendChild(shareButton);
  buttonContainer.appendChild(readButton);

  // إضافة زر إعادة تعيين للأذكار المتكررة (إذا لم يتم إضافته مسبقاً)
  if (zikr.count > 1) {
    const resetButton = document.createElement('button');
    resetButton.className = 'reset-zikr-button';
    resetButton.innerHTML = '🔄';
    resetButton.title = 'إعادة تعيين العداد';
    resetButton.addEventListener('click', (e) => {
      e.stopPropagation();
      resetZikrProgress(zikrId, zikrElement, readButton, zikr.count);
    });
    buttonContainer.appendChild(resetButton);
  }

  // ترتيب العناصر
  zikrElement.appendChild(audioControls);
  zikrElement.appendChild(zikrText);
  if (zikr.translation) {
    zikrElement.appendChild(zikrTranslation);
  }
  zikrElement.appendChild(zikrCount);
  zikrElement.appendChild(zikrCountContainer);
  zikrElement.appendChild(buttonContainer);

  // تحقق من حالة القراءة المحفوظة
  checkReadStatus(zikrId, zikrElement, readButton);

  return zikrElement;
}

// زيادة العداد
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';
    }
  }
}

// إعادة تعيين العداد
function resetCounter(display, maxCount) {
  display.textContent = `0 / ${maxCount}`;
  display.style.color = '';
  display.style.fontWeight = '';
}

// وظائف نظام تتبع القراءة المحسن
function markAsRead(zikrId, zikrElement, readButton, requiredCount = 1) {
  // التحقق من صحة المعاملات
  if (!zikrId || !zikrElement || !readButton) {
    console.error('معاملات غير صحيحة في markAsRead');
    return;
  }

  // الحصول على تاريخ اليوم
  const today = new Date().toDateString();

  // الحصول على البيانات المحفوظة
  chrome.storage.local.get(['readAzkar', 'dailyReadCount', 'zikrProgress'], (result) => {
    try {
      let readAzkar = result.readAzkar || {};
      let dailyReadCount = result.dailyReadCount || { date: today, count: 0 };
      let zikrProgress = result.zikrProgress || {};

      // إعادة تعيين العداد إذا كان يوم جديد
      if (dailyReadCount.date !== today) {
        dailyReadCount = { date: today, count: 0 };
        // إعادة تعيين تقدم الأذكار للأذكار غير المكتملة فقط
        Object.keys(zikrProgress).forEach(key => {
          if (!zikrProgress[key].completed) {
            zikrProgress[key] = { count: 0, completed: false };
          }
        });
      }

      // تهيئة تقدم الذكر إذا لم يكن موجود
      if (!zikrProgress[zikrId]) {
        zikrProgress[zikrId] = { count: 0, completed: false, lastRead: null };
      }

      // التحقق من عدم إكمال الذكر مسبقاً
      if (zikrProgress[zikrId].completed) {
        showMessage('تم إكمال هذا الذكر بالفعل اليوم', 'info');
        return;
      }

      // زيادة عداد الذكر
      zikrProgress[zikrId].count++;
      zikrProgress[zikrId].lastRead = new Date().toISOString();

      // تحديث عرض التقدم للأذكار المتكررة
      updateProgressDisplay(readButton, zikrProgress[zikrId].count, requiredCount);

      // التحقق من اكتمال الذكر
      if (zikrProgress[zikrId].count >= requiredCount) {
        completeZikr(zikrId, zikrElement, readButton, readAzkar, dailyReadCount, zikrProgress, today);
      } else {
        // تطبيق تأثيرات التقدم الجزئي
        applyProgressEffects(zikrElement, readButton, zikrProgress[zikrId].count, requiredCount);

        // تشغيل صوت تقدم خفيف
        playProgressSound();

        // عرض رسالة تشجيعية للتقدم
        showProgressMessage(zikrProgress[zikrId].count, requiredCount);
      }

      // حفظ البيانات
      saveProgressData(readAzkar, dailyReadCount, zikrProgress);

    } catch (error) {
      console.error('خطأ في markAsRead:', error);
      showMessage('حدث خطأ أثناء تسجيل القراءة', 'error');
    }
  });
}

// تحديث عرض التقدم
function updateProgressDisplay(readButton, currentCount, requiredCount) {
  if (requiredCount > 1) {
    const progressSpan = readButton.querySelector('.read-progress');
    if (progressSpan) {
      progressSpan.textContent = `${currentCount}/${requiredCount}`;

      // إضافة تأثير بصري للتحديث
      progressSpan.style.animation = 'countUp 0.3s ease-out';
      setTimeout(() => {
        progressSpan.style.animation = '';
      }, 300);
    }
  }
}

// إكمال الذكر
function completeZikr(zikrId, zikrElement, readButton, readAzkar, dailyReadCount, zikrProgress, today) {
  zikrProgress[zikrId].completed = true;
  zikrProgress[zikrId].completedAt = new Date().toISOString();

  // إضافة إلى قائمة الأذكار المكتملة
  if (!readAzkar[today]) {
    readAzkar[today] = [];
  }

  if (!readAzkar[today].includes(zikrId)) {
    readAzkar[today].push(zikrId);
    dailyReadCount.count++;

    // تطبيق التأثيرات البصرية للإكمال
    applyCompletionEffects(zikrElement, readButton);

    // تشغيل صوت التأكيد
    playConfirmationSound();

    // عرض رسالة تشجيعية
    showEncouragementMessage(dailyReadCount.count);

    // تحديث عداد الأذكار المقروءة
    updateDailyCounter(dailyReadCount.count);

    // فحص الإنجازات
    checkAchievements(dailyReadCount.count);

    // تسجيل في الإحصائيات المفصلة
    if (window.detailedStatisticsManager) {
      const category = zikrElement.closest('.tab-content')?.id || 'unknown';
      window.detailedStatisticsManager.recordZikrRead(zikrId, category);
    }

    // إضافة النقاط باستخدام النظام المحسن
    if (window.enhancedPointsSystem) {
      const category = zikrElement.closest('.tab-content')?.id || 'unknown';
      const zikrType = determineZikrType(zikrText);

      // إضافة فئة اليوم
      window.enhancedPointsSystem.addTodayCategory(category);

      // تسجيل قراءة مبكرة أو ليلية
      const hour = new Date().getHours();
      if (hour >= 5 && hour <= 7 && category === 'morning') {
        const today = new Date().toDateString();
        const earlyReads = JSON.parse(localStorage.getItem(`early_reads_${today}`) || '[]');
        earlyReads.push(Date.now());
        localStorage.setItem(`early_reads_${today}`, JSON.stringify(earlyReads));
      }

      if (hour >= 2 && hour <= 5) {
        const today = new Date().toDateString();
        const nightReads = JSON.parse(localStorage.getItem(`night_reads_${today}`) || '[]');
        nightReads.push(Date.now());
        localStorage.setItem(`night_reads_${today}`, JSON.stringify(nightReads));
      }

      // إضافة النقاط مع التأثيرات البصرية
      window.enhancedPointsSystem.addPoints(zikrType, category, true);
    }

    // إضافة تأثير احتفالي للإكمال
    addCelebrationEffect(zikrElement);
  }
}

// تحديد نوع الذكر للنقاط
function determineZikrType(zikrText) {
  if (!zikrText) return 'simple';

  const textLength = zikrText.length;

  // أذكار خاصة (تحتوي على كلمات مميزة)
  const specialKeywords = ['اللهم', 'سبحان الله', 'الحمد لله', 'لا إله إلا الله', 'الله أكبر'];
  const hasSpecialKeywords = specialKeywords.some(keyword => zikrText.includes(keyword));

  // أذكار نادرة (طويلة ومعقدة)
  if (textLength > 200 && hasSpecialKeywords) {
    return 'rare';
  }

  // أذكار خاصة (متوسطة مع كلمات مميزة)
  if (hasSpecialKeywords && textLength > 100) {
    return 'special';
  }

  // أذكار معقدة (طويلة)
  if (textLength > 150) {
    return 'complex';
  }

  // أذكار متوسطة
  if (textLength > 50) {
    return 'medium';
  }

  // أذكار بسيطة
  return 'simple';
}

// حفظ بيانات التقدم مع معالجة الأخطاء وتحسين الأداء
let saveTimeout;
function saveProgressData(readAzkar, dailyReadCount, zikrProgress) {
  // استخدام debouncing لتجنب الحفظ المتكرر
  clearTimeout(saveTimeout);
  saveTimeout = setTimeout(() => {
    chrome.storage.local.set({
      readAzkar: readAzkar,
      dailyReadCount: dailyReadCount,
      zikrProgress: zikrProgress
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في حفظ بيانات التقدم:', chrome.runtime.lastError);
        showMessage('فشل في حفظ التقدم', 'error');
      }
    });
  }, 500); // تأخير 500ms لتجميع عدة عمليات حفظ
}

// تحسين أداء عرض الرسائل
let messageTimeout;
function showMessage(message, type = 'info') {
  // منع عرض رسائل متعددة في نفس الوقت
  clearTimeout(messageTimeout);

  // استخدام نظام الرسائل الموجود مع تحسينات
  if (typeof showSuccessMessage === 'function' && type === 'success') {
    showSuccessMessage(message);
  } else if (typeof showErrorMessage === 'function' && type === 'error') {
    showErrorMessage(message);
  } else {
    // إنشاء رسالة مخصصة إذا لم تكن الوظائف متاحة
    showCustomMessage(message, type);
  }
}

// عرض رسالة مخصصة محسنة
function showCustomMessage(message, type) {
  // إزالة الرسائل السابقة
  const existingMessages = document.querySelectorAll('.custom-message');
  existingMessages.forEach(msg => msg.remove());

  const messageDiv = document.createElement('div');
  messageDiv.className = `custom-message ${type}`;
  messageDiv.textContent = message;
  messageDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease-out;
    background-color: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
  `;

  document.body.appendChild(messageDiv);

  // إزالة الرسالة بعد 3 ثوان
  messageTimeout = setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.style.animation = 'slideOut 0.3s ease-in forwards';
      setTimeout(() => {
        if (messageDiv.parentNode) {
          messageDiv.parentNode.removeChild(messageDiv);
        }
      }, 300);
    }
  }, 3000);
}

// إعادة تعيين تقدم ذكر معين
function resetZikrProgress(zikrId, zikrElement, readButton, requiredCount) {
  if (!confirm('هل تريد إعادة تعيين تقدم هذا الذكر؟')) {
    return;
  }

  const today = new Date().toDateString();

  chrome.storage.local.get(['readAzkar', 'dailyReadCount', 'zikrProgress'], (result) => {
    try {
      let readAzkar = result.readAzkar || {};
      let dailyReadCount = result.dailyReadCount || { date: today, count: 0 };
      let zikrProgress = result.zikrProgress || {};

      // إعادة تعيين تقدم الذكر
      if (zikrProgress[zikrId]) {
        // إذا كان الذكر مكتملاً، قلل من العداد اليومي
        if (zikrProgress[zikrId].completed && readAzkar[today] && readAzkar[today].includes(zikrId)) {
          const index = readAzkar[today].indexOf(zikrId);
          readAzkar[today].splice(index, 1);
          dailyReadCount.count = Math.max(0, dailyReadCount.count - 1);
          updateDailyCounter(dailyReadCount.count);
        }

        // إعادة تعيين البيانات
        zikrProgress[zikrId] = { count: 0, completed: false, lastRead: null };
      }

      // إعادة تعيين الواجهة
      resetZikrUI(zikrElement, readButton, requiredCount);

      // حفظ البيانات
      saveProgressData(readAzkar, dailyReadCount, zikrProgress);

      showMessage('تم إعادة تعيين تقدم الذكر', 'success');

    } catch (error) {
      console.error('خطأ في إعادة تعيين تقدم الذكر:', error);
      showMessage('حدث خطأ أثناء إعادة التعيين', 'error');
    }
  });
}

// إعادة تعيين واجهة المستخدم للذكر
function resetZikrUI(zikrElement, readButton, requiredCount) {
  // إعادة تعيين شكل الزر
  readButton.innerHTML = '✅';
  readButton.className = 'read-button';
  readButton.title = requiredCount > 1 ? `اقرأ ${requiredCount} مرات` : 'تم القراءة';
  readButton.disabled = false;

  // إعادة تعيين العداد للأذكار المتكررة
  if (requiredCount > 1) {
    const readCounter = document.createElement('div');
    readCounter.className = 'read-counter';
    readCounter.innerHTML = `<span class="read-progress">0/${requiredCount}</span>`;
    readButton.appendChild(readCounter);
  }

  // إعادة تعيين تصميم الذكر
  zikrElement.className = 'zikr-item';
  zikrElement.style.background = '';
  zikrElement.style.borderLeft = '';
  zikrElement.style.transform = '';
  zikrElement.style.boxShadow = '';

  // إزالة أي تأثيرات احتفالية
  const celebration = zikrElement.querySelector('.celebration-effect');
  if (celebration) {
    celebration.remove();
  }
}

// عرض رسالة تشجيعية للتقدم
function showProgressMessage(currentCount, requiredCount) {
  const messages = [
    `ممتاز! ${currentCount} من ${requiredCount}`,
    `استمر! باقي ${requiredCount - currentCount}`,
    `أحسنت! تقدم رائع`,
    `بارك الله فيك! واصل`
  ];

  const randomMessage = messages[Math.floor(Math.random() * messages.length)];
  showMessage(randomMessage, 'success');
}

// إضافة تأثير احتفالي للإكمال
function addCelebrationEffect(zikrElement) {
  // إضافة تأثير الكونفيتي
  const celebration = document.createElement('div');
  celebration.className = 'celebration-effect';
  celebration.innerHTML = '🎉✨🌟';
  celebration.style.position = 'absolute';
  celebration.style.top = '0';
  celebration.style.right = '0';
  celebration.style.fontSize = '20px';
  celebration.style.animation = 'celebrate 2s ease-out forwards';
  celebration.style.pointerEvents = 'none';
  celebration.style.zIndex = '1000';

  zikrElement.style.position = 'relative';
  zikrElement.appendChild(celebration);

  // إزالة التأثير بعد انتهاء الأنيميشن
  setTimeout(() => {
    if (celebration.parentNode) {
      celebration.parentNode.removeChild(celebration);
    }
  }, 2000);
}

function checkReadStatus(zikrId, zikrElement, readButton) {
  const today = new Date().toDateString();
  const requiredCount = parseInt(zikrElement.getAttribute('data-zikr-count')) || 1;

  chrome.storage.local.get(['readAzkar', 'zikrProgress'], (result) => {
    try {
      const readAzkar = result.readAzkar || {};
      const zikrProgress = result.zikrProgress || {};

      // التحقق من الإكمال الكامل
      if (readAzkar[today] && readAzkar[today].includes(zikrId)) {
        applyCompletionEffects(zikrElement, readButton);
      }
      // التحقق من التقدم الجزئي
      else if (zikrProgress[zikrId] && zikrProgress[zikrId].count > 0 && !zikrProgress[zikrId].completed) {
        const currentCount = zikrProgress[zikrId].count;

        // تحديث عرض التقدم
        updateProgressDisplay(readButton, currentCount, requiredCount);

        // تطبيق تأثيرات التقدم
        applyProgressEffects(zikrElement, readButton, currentCount, requiredCount);
      }
    } catch (error) {
      console.error('خطأ في checkReadStatus:', error);
    }
  });
}

// تطبيق تأثيرات الإكمال الكامل
function applyCompletionEffects(zikrElement, readButton) {
  // تغيير شكل الزر
  readButton.innerHTML = '✅ مكتمل';
  readButton.classList.add('completed');
  readButton.title = 'تم إكمال الذكر';
  readButton.disabled = true;

  // إضافة تأثير بصري للذكر
  zikrElement.classList.add('read-zikr', 'fade-in-up');

  // تطبيق تأثيرات الجسيمات
  if (window.visualEffectsManager) {
    window.visualEffectsManager.createReadingParticles(zikrElement);
    window.visualEffectsManager.applyGlowEffect(zikrElement);
  }

  // تأثير fade مؤقت
  zikrElement.style.transition = 'all 0.5s ease';
  zikrElement.style.transform = 'scale(1.02)';
  zikrElement.style.boxShadow = '0 4px 20px rgba(76, 175, 80, 0.3)';

  setTimeout(() => {
    zikrElement.style.transform = 'scale(1)';
    zikrElement.style.boxShadow = '';
    zikrElement.classList.remove('fade-in-up');
  }, 500);
}

// تطبيق تأثيرات التقدم الجزئي
function applyProgressEffects(zikrElement, readButton, currentCount, requiredCount) {
  const progressPercentage = (currentCount / requiredCount) * 100;

  // تغيير لون الزر تدريجياً
  if (progressPercentage < 50) {
    readButton.style.backgroundColor = '#FFC107'; // أصفر
    readButton.style.color = '#333';
  } else if (progressPercentage < 100) {
    readButton.style.backgroundColor = '#FF9800'; // برتقالي
    readButton.style.color = 'white';
  }

  // إضافة تأثير تقدم للذكر
  zikrElement.style.borderRight = `4px solid #FFC107`;
  zikrElement.style.background = `linear-gradient(90deg, #fff3cd ${progressPercentage}%, #ffffff ${progressPercentage}%)`;

  // تأثير نبضة خفيفة
  readButton.style.animation = 'pulse 0.3s ease-in-out';
  setTimeout(() => {
    readButton.style.animation = '';
  }, 300);
}

// تشغيل صوت التقدم
function playProgressSound() {
  chrome.storage.local.get(['audioSettings'], (result) => {
    const settings = result.audioSettings || { enabled: true, confirmationSound: true };

    if (settings.enabled && settings.confirmationSound) {
      try {
        // إنشاء AudioContext مع دعم المتصفحات القديمة
        const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
        if (!AudioContextClass) {
          console.log('AudioContext غير مدعوم في هذا المتصفح');
          return;
        }

        const audioContext = new AudioContextClass();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
      } catch (error) {
        console.log('تعذر تشغيل الصوت:', error);
      }
    }
  });
}

function applyReadEffects(zikrElement, readButton) {
  // تغيير شكل الزر
  readButton.innerHTML = '✅';
  readButton.classList.add('completed');
  readButton.title = 'تمت القراءة';

  // إضافة تأثير بصري للذكر
  zikrElement.classList.add('read-zikr', 'fade-in-up');

  // تطبيق تأثيرات الجسيمات
  if (window.visualEffectsManager) {
    window.visualEffectsManager.createReadingParticles(zikrElement);
    window.visualEffectsManager.applyGlowEffect(zikrElement);
  }

  // تأثير fade مؤقت
  zikrElement.style.transition = 'all 0.5s ease';
  zikrElement.style.transform = 'scale(1.02)';
  zikrElement.style.boxShadow = '0 4px 20px rgba(76, 175, 80, 0.3)';

  setTimeout(() => {
    zikrElement.style.transform = 'scale(1)';
    zikrElement.style.boxShadow = '';
    zikrElement.classList.remove('fade-in-up');
  }, 500);
}

function playConfirmationSound() {
  // تشغيل صوت تأكيد بسيط (اختياري)
  chrome.storage.local.get(['audioSettings'], (result) => {
    const settings = result.audioSettings || { enabled: true, confirmationSound: true };

    if (settings.enabled && settings.confirmationSound) {
      try {
        // إنشاء AudioContext مع دعم المتصفحات القديمة
        const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
        if (!AudioContextClass) {
          console.log('AudioContext غير مدعوم في هذا المتصفح');
          return;
        }

        const audioContext = new AudioContextClass();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
      } catch (error) {
        console.log('تعذر تشغيل صوت التأكيد:', error);
      }
    }
  });
}

function showEncouragementMessage(count) {
  const messages = [
    'بارك الله فيك! 🌟',
    'أحسنت! استمر في الذكر 💚',
    'جزاك الله خيراً 🤲',
    'ما شاء الله! 🌙',
    'بارك الله في عملك الصالح ⭐',
    'اللهم بارك! 🕌',
    'أثابك الله 💎',
    'نفع الله بك 🌟'
  ];

  let message = messages[Math.floor(Math.random() * messages.length)];

  // رسائل خاصة للإنجازات
  if (count === 5) message = 'ممتاز! قرأت 5 أذكار اليوم! 🎉';
  else if (count === 10) message = 'رائع! 10 أذكار مكتملة! 🏆';
  else if (count === 20) message = 'مبارك! 20 ذكر في يوم واحد! 🌟';
  else if (count === 50) message = 'سبحان الله! 50 ذكر! أنت مثال يُحتذى به! 👑';

  showSuccessMessage(message);
}

function updateDailyCounter(count) {
  const counterElement = document.getElementById('daily-read-counter');
  if (counterElement) {
    counterElement.textContent = count;

    // تأثيرات بصرية محسنة للعداد
    if (window.visualEffectsManager) {
      window.visualEffectsManager.pulseCounter(counterElement);
    }

    // تأثيرات خاصة للإنجازات المهمة
    if (count === 5 || count === 10 || count === 20 || count === 50) {
      counterElement.classList.add('milestone', 'rainbow-text');

      // تأثير الاحتفال
      if (window.visualEffectsManager) {
        window.visualEffectsManager.createCelebrationEffect();
      }

      setTimeout(() => {
        counterElement.classList.remove('milestone', 'rainbow-text');
      }, 3000);
    }

    // تحديث الرسم البياني
    if (window.visualEffectsManager) {
      window.visualEffectsManager.updateProgressChart();
    }
  }
}

function checkAchievements(count) {
  const achievements = [
    { count: 5, title: 'البداية المباركة', description: 'قرأت 5 أذكار في يوم واحد', icon: '🌟' },
    { count: 10, title: 'المثابر', description: 'قرأت 10 أذكار في يوم واحد', icon: '🏆' },
    { count: 20, title: 'المجتهد', description: 'قرأت 20 ذكر في يوم واحد', icon: '💎' },
    { count: 50, title: 'المتميز', description: 'قرأت 50 ذكر في يوم واحد', icon: '👑' }
  ];

  const achievement = achievements.find(a => a.count === count);
  if (achievement) {
    showAchievementNotification(achievement);
  }
}

function showAchievementNotification(achievement) {
  const notification = document.createElement('div');
  notification.className = 'achievement-notification';
  notification.innerHTML = `
    <div class="achievement-icon">${achievement.icon}</div>
    <div class="achievement-content">
      <h3>إنجاز جديد!</h3>
      <h4>${achievement.title}</h4>
      <p>${achievement.description}</p>
    </div>
  `;

  document.body.appendChild(notification);

  // إزالة الإشعار بعد 5 ثوان
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 5000);
}

// تحميل عداد الأذكار اليومي
function loadDailyCounter() {
  const today = new Date().toDateString();

  chrome.storage.local.get(['dailyReadCount'], (result) => {
    let dailyReadCount = result.dailyReadCount || { date: today, count: 0 };

    // إعادة تعيين العداد إذا كان يوم جديد
    if (dailyReadCount.date !== today) {
      dailyReadCount = { date: today, count: 0 };
      chrome.storage.local.set({ dailyReadCount: dailyReadCount });
    }

    // تحديث العداد في الواجهة
    updateDailyCounter(dailyReadCount.count);
  });
}

// إعادة تعيين العداد في منتصف الليل
function setupMidnightReset() {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);

  const msUntilMidnight = tomorrow.getTime() - now.getTime();

  setTimeout(() => {
    // إعادة تعيين العداد
    const today = new Date().toDateString();
    const dailyReadCount = { date: today, count: 0 };

    chrome.storage.local.set({
      dailyReadCount: dailyReadCount,
      readAzkar: {} // مسح أذكار اليوم السابق
    });

    // تحديث الواجهة
    updateDailyCounter(0);

    // إعادة تحميل جميع الأذكار لإزالة علامات القراءة
    location.reload();

    // إعداد التوقيت للليلة القادمة
    setupMidnightReset();
  }, msUntilMidnight);
}

// وظائف الصوت
function loadAudioSettings() {
  chrome.storage.local.get(['audioSettings'], (result) => {
    const settings = result.audioSettings || { enabled: true, volume: 0.7 };
    audioEnabled = settings.enabled;

    // إضافة عنصر تحكم الصوت في الإعدادات إذا لم يكن موجود
    addAudioControlsToSettings();
  });
}

function addAudioControlsToSettings() {
  const settingsForm = document.querySelector('.settings-form');
  if (settingsForm && !document.getElementById('audio-enabled')) {
    const audioGroup = document.createElement('div');
    audioGroup.className = 'form-group';
    audioGroup.innerHTML = `
      <label for="audio-enabled">تفعيل التلاوة الصوتية:</label>
      <input type="checkbox" id="audio-enabled" name="audio-enabled" ${audioEnabled ? 'checked' : ''}>
    `;

    const volumeGroup = document.createElement('div');
    volumeGroup.className = 'form-group';
    volumeGroup.innerHTML = `
      <label for="audio-volume">مستوى الصوت:</label>
      <input type="range" id="audio-volume" name="audio-volume" min="0" max="1" step="0.1" value="0.7">
    `;

    settingsForm.insertBefore(audioGroup, settingsForm.lastElementChild);
    settingsForm.insertBefore(volumeGroup, settingsForm.lastElementChild);
  }
}

function playAudio(audioPath) {
  if (!audioEnabled) return;

  try {
    // إيقاف الصوت الحالي إذا كان يعمل
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
    }

    // تشغيل الصوت الجديد
    currentAudio = new Audio(chrome.runtime.getURL(audioPath));
    currentAudio.volume = document.getElementById('audio-volume')?.value || 0.7;

    currentAudio.play().catch(error => {
      console.warn('لا يمكن تشغيل الملف الصوتي:', error);
      // عرض رسالة للمستخدم
      showAudioError('لا يمكن تشغيل الملف الصوتي. تأكد من وجود الملفات الصوتية.');
    });

  } catch (error) {
    console.error('خطأ في تشغيل الصوت:', error);
  }
}

function stopAudio() {
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.currentTime = 0;
    currentAudio = null;
  }
}

// عرض خطأ الصوت
function showAudioError(message) {
  // إنشاء عنصر الخطأ
  const errorDiv = document.createElement('div');
  errorDiv.className = 'audio-error-message';
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f44336;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 14px;
    max-width: 300px;
  `;

  document.body.appendChild(errorDiv);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 3000);
}

function getAudioPath(zikrText, category) {
  // تحديد مسار الملف الصوتي بناءً على نص الذكر والفئة
  const audioMap = {
    'آية الكرسي': 'audio/' + category + '/ayat-kursi.mp3',
    'سورة الإخلاص': 'audio/' + category + '/ikhlas.mp3',
    'سورة الفلق': 'audio/' + category + '/falaq.mp3',
    'سورة الناس': 'audio/' + category + '/nas.mp3',
    'سبحان الله': 'audio/prayer/tasbih.mp3',
    'الحمد لله': 'audio/prayer/tahmid.mp3',
    'الله أكبر': 'audio/prayer/takbir.mp3'
  };

  // البحث عن تطابق في النص
  for (const [key, path] of Object.entries(audioMap)) {
    if (zikrText.includes(key)) {
      return path;
    }
  }

  return null; // لا يوجد ملف صوتي متاح
}

// إعداد التبويبات
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع التبويبات
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // إضافة الفئة النشطة للتبويب المحدد
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(tabId).classList.add('active');
    });
  });
}

// تهيئة أوقات الصلاة
async function initPrayerTimes() {
  try {
    if (!window.prayerTimesManager) {
      console.warn('مدير أوقات الصلاة غير متاح');
      return;
    }

    // محاولة تحميل أوقات الصلاة المحفوظة أولاً
    let prayerTimes = await prayerTimesManager.loadSavedPrayerTimes();

    if (prayerTimes) {
      displayPrayerTimes(prayerTimes);
    } else {
      showPrayerTimesLoading();
    }

    // تحديث أوقات الصلاة
    try {
      prayerTimes = await prayerTimesManager.updatePrayerTimes();
      displayPrayerTimes(prayerTimes);

      // إعداد تذكيرات الصلاة
      await prayerTimesManager.setupPrayerReminders();
    } catch (error) {
      showPrayerTimesError(error.message);
    }
  } catch (error) {
    console.error('خطأ في تهيئة أوقات الصلاة:', error);
  }
}

// عرض أوقات الصلاة
function displayPrayerTimes(prayerTimes) {
  const grid = document.getElementById('prayer-times-grid');
  const nextPrayerInfo = document.getElementById('next-prayer-info');
  const hijriDateDisplay = document.getElementById('hijri-date-display');

  if (!grid) return;

  // عرض أوقات الصلاة
  const prayers = [
    { key: 'fajr', name: 'الفجر', time: prayerTimes.timings.fajr },
    { key: 'dhuhr', name: 'الظهر', time: prayerTimes.timings.dhuhr },
    { key: 'asr', name: 'العصر', time: prayerTimes.timings.asr },
    { key: 'maghrib', name: 'المغرب', time: prayerTimes.timings.maghrib },
    { key: 'isha', name: 'العشاء', time: prayerTimes.timings.isha }
  ];

  // الحصول على الصلاة القادمة
  const nextPrayer = prayerTimesManager.getNextPrayer();

  grid.innerHTML = prayers.map(prayer => {
    const isNext = nextPrayer && nextPrayer.name === prayer.key;
    return `
      <div class="prayer-time-item ${isNext ? 'next-prayer' : ''}">
        <div class="prayer-name">${prayer.name}</div>
        <div class="prayer-time">${prayer.time.time12}</div>
      </div>
    `;
  }).join('');

  // عرض معلومات الصلاة القادمة
  if (nextPrayer && nextPrayerInfo) {
    const timeRemaining = nextPrayer.timeRemaining;
    if (timeRemaining) {
      nextPrayerInfo.textContent = `${nextPrayer.arabicName} خلال ${timeRemaining.formatted}`;
    } else {
      nextPrayerInfo.textContent = `${nextPrayer.arabicName} في ${nextPrayer.time.time12}`;
    }
  }

  // عرض التاريخ الهجري
  const hijriDate = prayerTimesManager.getHijriDate();
  if (hijriDate && hijriDateDisplay) {
    hijriDateDisplay.textContent = hijriDate.formatted;
  }
}

// عرض حالة التحميل
function showPrayerTimesLoading() {
  const grid = document.getElementById('prayer-times-grid');
  if (grid) {
    grid.innerHTML = '<div class="loading">جاري تحميل أوقات الصلاة...</div>';
  }
}

// عرض خطأ أوقات الصلاة
function showPrayerTimesError(errorMessage) {
  const grid = document.getElementById('prayer-times-grid');
  if (grid) {
    grid.innerHTML = `
      <div class="prayer-times-error">
        <p>⚠️ خطأ في تحميل أوقات الصلاة</p>
        <p>${errorMessage}</p>
        <div class="location-permission">
          <p>يرجى السماح بالوصول للموقع الجغرافي</p>
          <button id="request-location-btn">السماح بالوصول للموقع</button>
        </div>
      </div>
    `;

    // إضافة event listener للزر
    setTimeout(() => {
      const requestLocationBtn = document.getElementById('request-location-btn');
      if (requestLocationBtn) {
        requestLocationBtn.addEventListener('click', requestLocationPermission);
      }
    }, 100);
  }
}

// طلب إذن الموقع
async function requestLocationPermission() {
  try {
    await prayerTimesManager.getCurrentLocation();
    initPrayerTimes(); // إعادة المحاولة
  } catch (error) {
    alert('لا يمكن الوصول للموقع الجغرافي. يرجى تفعيل خدمات الموقع في المتصفح.');
  }
}

// تهيئة اختيار الثيمات
function initThemeSelector() {
  const themeSelector = document.getElementById('theme-selector');
  if (!themeSelector || !window.themeManager) return;

  const themes = themeManager.getAllThemes();
  const currentTheme = themeManager.currentTheme;

  themeSelector.innerHTML = Object.entries(themes).map(([id, theme]) => `
    <div class="theme-option ${id === currentTheme ? 'active' : ''}" data-theme-id="${id}">
      <div class="theme-icon">${theme.icon}</div>
      <div class="theme-name">${theme.name}</div>
      <div class="theme-description">${theme.description}</div>
    </div>
  `).join('');

  // إضافة مستمعي الأحداث
  themeSelector.addEventListener('click', (e) => {
    const themeOption = e.target.closest('.theme-option');
    if (themeOption) {
      const themeId = themeOption.dataset.themeId;

      // إزالة التحديد السابق
      themeSelector.querySelectorAll('.theme-option').forEach(option => {
        option.classList.remove('active');
      });

      // تحديد الثيم الجديد
      themeOption.classList.add('active');

      // تطبيق الثيم
      themeManager.applyTheme(themeId);
    }
  });
}

// تهيئة التقويم الهجري
async function initHijriCalendar() {
  try {
    if (!window.hijriCalendarManager) {
      console.warn('مدير التقويم الهجري غير متاح');
      return;
    }

    // عرض التاريخ الهجري
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (hijriDate) {
      const hijriDisplay = document.getElementById('hijri-date-display');
      if (hijriDisplay) {
        hijriDisplay.textContent = hijriDate.formatted;
      }

      // عرض معلومات الشهر
      displayHijriMonthInfo();
    }

    // عرض المناسبات الإسلامية
    displayIslamicEvents();

    // إعداد تذكيرات المناسبات
    await hijriCalendarManager.setupEventReminders();

  } catch (error) {
    console.error('خطأ في تهيئة التقويم الهجري:', error);
  }
}

// عرض معلومات الشهر الهجري
function displayHijriMonthInfo() {
  const monthInfo = hijriCalendarManager.getCurrentMonthInfo();
  if (!monthInfo) return;

  const hijriMonthInfoDiv = document.getElementById('hijri-month-info');
  const hijriProgress = document.getElementById('hijri-progress');
  const hijriProgressText = document.getElementById('hijri-progress-text');

  if (hijriMonthInfoDiv && hijriProgress && hijriProgressText) {
    hijriMonthInfoDiv.style.display = 'block';
    hijriProgress.style.width = `${monthInfo.progress}%`;
    hijriProgressText.textContent = `${monthInfo.day}/${monthInfo.monthLength} أيام`;
  }
}

// عرض المناسبات الإسلامية
function displayIslamicEvents() {
  const eventsList = document.getElementById('events-list');
  if (!eventsList) return;

  const upcomingEvents = hijriCalendarManager.getUpcomingEvents(7);

  if (upcomingEvents.length === 0) {
    eventsList.innerHTML = '<div class="no-events">لا توجد مناسبات قريبة</div>';
    return;
  }

  eventsList.innerHTML = upcomingEvents.slice(0, 5).map(event => {
    const eventIcon = getEventIcon(event.type);
    const daysText = event.daysFromNow === 0 ? 'اليوم' :
                     event.daysFromNow === 1 ? 'غداً' :
                     `خلال ${event.daysFromNow} أيام`;

    return `
      <div class="event-item event-importance-${event.importance}">
        <div class="event-icon">${eventIcon}</div>
        <div class="event-details">
          <div class="event-name">${event.name}</div>
          <div class="event-date">${daysText}</div>
        </div>
      </div>
    `;
  }).join('');
}

// الحصول على أيقونة المناسبة
function getEventIcon(eventType) {
  const icons = {
    'celebration': '🎉',
    'religious': '🕌',
    'ramadan': '🌙',
    'eid': '🎊',
    'hajj': '🕋',
    'sunnah': '⭐'
  };
  return icons[eventType] || '📅';
}

// تهيئة أذكار رمضان
async function initRamadanAzkar() {
  try {
    if (!window.ramadanAzkarManager) {
      console.warn('مدير أذكار رمضان غير متاح');
      return;
    }

    // التحقق من شهر رمضان
    const isRamadan = await ramadanAzkarManager.checkAndActivateRamadan();

    // إظهار/إخفاء تبويب رمضان
    const ramadanTab = document.querySelector('.ramadan-tab');
    if (ramadanTab) {
      ramadanTab.style.display = isRamadan ? 'block' : 'none';
    }

    if (isRamadan) {
      // عرض معلومات رمضان
      displayRamadanInfo();

      // عرض أذكار رمضان
      displayRamadanAzkar();

      // عرض نصائح رمضان
      displayRamadanTips();

      // عرض إحصائيات رمضان
      displayRamadanStats();
    }

  } catch (error) {
    console.error('خطأ في تهيئة أذكار رمضان:', error);
  }
}

// عرض معلومات رمضان
function displayRamadanInfo() {
  const ramadanInfo = ramadanAzkarManager.getRamadanInfo();
  if (!ramadanInfo) return;

  const ramadanDay = document.getElementById('ramadan-day');
  const daysRemaining = document.getElementById('days-remaining');
  const specialNight = document.getElementById('special-night');

  if (ramadanDay) ramadanDay.textContent = ramadanInfo.currentDay;
  if (daysRemaining) daysRemaining.textContent = ramadanInfo.daysRemaining;

  if (specialNight && ramadanInfo.isPossibleLaylatQadr) {
    specialNight.textContent = '✨ ليلة وترية محتملة لليلة القدر';
    specialNight.style.display = 'block';
  }

  // إظهار أقسام خاصة
  const laylatQadrSection = document.getElementById('laylat-qadr-section');
  const lastTenSection = document.getElementById('last-ten-section');

  if (laylatQadrSection && ramadanInfo.isPossibleLaylatQadr) {
    laylatQadrSection.style.display = 'block';
  }

  if (lastTenSection && ramadanInfo.isLastTenNights) {
    lastTenSection.style.display = 'block';
  }
}

// عرض أذكار رمضان
function displayRamadanAzkar() {
  // أذكار الإفطار
  const iftarAzkar = ramadanAzkarManager.getAzkarByType('iftar');
  displayAzkarInContainer('iftar-azkar', iftarAzkar, 'ramadan-iftar');

  // أذكار السحور
  const suhoorAzkar = ramadanAzkarManager.getAzkarByType('suhoor');
  displayAzkarInContainer('suhoor-azkar', suhoorAzkar, 'ramadan-suhoor');

  // أذكار ليلة القدر
  const laylatQadrAzkar = ramadanAzkarManager.getAzkarByType('laylatAlQadr');
  displayAzkarInContainer('laylat-qadr-azkar', laylatQadrAzkar, 'ramadan-laylat-qadr');

  // أذكار العشر الأواخر
  const lastTenAzkar = ramadanAzkarManager.getAzkarByType('lastTenNights');
  displayAzkarInContainer('last-ten-azkar', lastTenAzkar, 'ramadan-last-ten');
}

// عرض نصائح رمضان
function displayRamadanTips() {
  const tipsList = document.getElementById('tips-list');
  if (!tipsList) return;

  const tips = ramadanAzkarManager.getTodayRamadanTips();

  tipsList.innerHTML = tips.map(tip =>
    `<div class="tip-item">${tip}</div>`
  ).join('');
}

// عرض إحصائيات رمضان
async function displayRamadanStats() {
  const stats = await ramadanAzkarManager.getRamadanStats();
  if (!stats) return;

  const iftarCount = document.getElementById('iftar-count');
  const suhoorCount = document.getElementById('suhoor-count');
  const qadrCount = document.getElementById('qadr-count');

  if (iftarCount) iftarCount.textContent = stats.iftarAzkarRead;
  if (suhoorCount) suhoorCount.textContent = stats.suhoorAzkarRead;
  if (qadrCount) qadrCount.textContent = stats.laylatQadrAzkarRead;
}

// تهيئة نظام الإنجازات
async function initAchievements() {
  try {
    if (!window.achievementsManager) {
      console.warn('مدير الإنجازات غير متاح');
      return;
    }

    // عرض إحصائيات المستخدم
    displayUserStats();

    // عرض التحديات النشطة
    displayActiveChallenges();

    // عرض معرض الإنجازات
    displayAchievementsGallery();

    // عرض الإنجازات الحديثة
    displayRecentAchievements();

    // إعداد مرشحات الإنجازات
    setupAchievementFilters();

  } catch (error) {
    console.error('خطأ في تهيئة نظام الإنجازات:', error);
  }
}

// عرض إحصائيات المستخدم
function displayUserStats() {
  const stats = achievementsManager.getUserStats();

  // تحديث مستوى المستخدم
  const userLevel = document.getElementById('user-level');
  const levelDisplay = document.getElementById('level-display');
  const levelProgress = document.getElementById('level-progress');
  const levelProgressText = document.getElementById('level-progress-text');

  if (userLevel) userLevel.textContent = stats.level;
  if (levelDisplay) levelDisplay.textContent = stats.level;
  if (levelProgress) levelProgress.style.width = `${stats.levelProgress}%`;
  if (levelProgressText) {
    const nextLevelPoints = (stats.level * 100);
    levelProgressText.textContent = `${stats.totalPoints}/${nextLevelPoints} نقطة`;
  }

  // تحديث الإحصائيات العامة
  const totalReads = document.getElementById('total-reads');
  const currentStreak = document.getElementById('current-streak');
  const totalPoints = document.getElementById('total-points');
  const achievementsCount = document.getElementById('achievements-count');

  if (totalReads) totalReads.textContent = stats.totalReads;
  if (currentStreak) currentStreak.textContent = stats.currentStreak;
  if (totalPoints) totalPoints.textContent = stats.totalPoints;
  if (achievementsCount) achievementsCount.textContent = stats.achievementsCount;
}

// عرض التحديات النشطة
function displayActiveChallenges() {
  const challengesList = document.getElementById('challenges-list');
  if (!challengesList) return;

  const challenges = achievementsManager.getActiveChallenges();

  if (challenges.length === 0) {
    challengesList.innerHTML = '<div class="no-challenges">لا توجد تحديات نشطة</div>';
    return;
  }

  challengesList.innerHTML = challenges.map(challenge => `
    <div class="challenge-item">
      <div class="challenge-header">
        <div class="challenge-name">${challenge.name}</div>
        <div class="challenge-points">+${challenge.points} نقطة</div>
      </div>
      <div class="challenge-description">${challenge.description}</div>
      <div class="challenge-progress">
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${challenge.progressPercent}%"></div>
        </div>
        <div class="challenge-progress-text">${challenge.progress}/${challenge.target}</div>
      </div>
    </div>
  `).join('');
}

// عرض معرض الإنجازات
function displayAchievementsGallery() {
  const achievementsGrid = document.getElementById('achievements-grid');
  if (!achievementsGrid) return;

  const leaderboard = achievementsManager.getLeaderboard();

  achievementsGrid.innerHTML = leaderboard.map(achievement => `
    <div class="achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}">
      <div class="achievement-icon">${achievement.icon}</div>
      <div class="achievement-name">${achievement.name}</div>
      <div class="achievement-description">${achievement.description}</div>
      <div class="achievement-points">+${achievement.points} نقطة</div>
    </div>
  `).join('');
}

// عرض الإنجازات الحديثة
async function displayRecentAchievements() {
  const recentList = document.getElementById('recent-achievements');
  if (!recentList) return;

  const recentAchievements = await achievementsManager.getRecentAchievements();

  if (recentAchievements.length === 0) {
    recentList.innerHTML = '<div class="no-achievements">لا توجد إنجازات حديثة</div>';
    return;
  }

  recentList.innerHTML = recentAchievements.map(achievement => `
    <div class="recent-achievement">
      <div class="achievement-icon">${achievement.icon}</div>
      <div class="recent-achievement-details">
        <div class="recent-achievement-name">${achievement.name}</div>
        <div class="recent-achievement-points">+${achievement.points} نقطة</div>
      </div>
    </div>
  `).join('');
}

// إعداد مرشحات الإنجازات
function setupAchievementFilters() {
  const filterBtns = document.querySelectorAll('.filter-btn');

  filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      // إزالة التحديد السابق
      filterBtns.forEach(b => b.classList.remove('active'));

      // تحديد الزر الجديد
      btn.classList.add('active');

      // تطبيق المرشح
      const filter = btn.dataset.filter;
      filterAchievements(filter);
    });
  });
}

// تطبيق مرشح الإنجازات
function filterAchievements(filter) {
  const achievementCards = document.querySelectorAll('.achievement-card');

  achievementCards.forEach(card => {
    const isUnlocked = card.classList.contains('unlocked');

    switch (filter) {
      case 'all':
        card.style.display = 'block';
        break;
      case 'unlocked':
        card.style.display = isUnlocked ? 'block' : 'none';
        break;
      case 'locked':
        card.style.display = !isUnlocked ? 'block' : 'none';
        break;
    }
  });
}

// عرض الأذكار في حاوية محددة
function displayAzkarInContainer(containerId, azkarList, category) {
  const container = document.getElementById(containerId);
  if (!container) return;

  container.innerHTML = azkarList.map(zikr => {
    const zikrElement = createZikrElement(zikr, category);
    return zikrElement.outerHTML;
  }).join('');

  // إضافة مستمعي الأحداث للعدادات
  container.querySelectorAll('.counter-display').forEach(display => {
    display.addEventListener('click', () => {
      const maxCount = parseInt(display.dataset.maxCount);
      incrementCounter(display, maxCount);

      // تسجيل قراءة الذكر للإنجازات
      if (window.achievementsManager) {
        achievementsManager.recordAzkarRead(category);
      }

      // تحديث إحصائيات رمضان إذا كان ذكر رمضان
      if (category.startsWith('ramadan-')) {
        const ramadanType = category.replace('ramadan-', '');
        ramadanAzkarManager.updateRamadanStats(ramadanType);
      }
    });
  });
}

// تحديث العداد مع ربطه بنظام الإنجازات
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';

      // إضافة تأثير بصري للإكمال
      display.parentElement.classList.add('completed');
      setTimeout(() => {
        display.parentElement.classList.remove('completed');
      }, 2000);
    }
  }
}

// وظيفة مشاركة الذكر
function shareZikr(zikrText, translation = '') {
  if (window.shareManager) {
    shareManager.showShareDialog(zikrText, translation);
  } else {
    // نسخ النص كبديل
    const textToShare = `${zikrText}\n\n${translation ? translation + '\n\n' : ''}من تطبيق أذكار المسلم`;
    navigator.clipboard.writeText(textToShare).then(() => {
      showSuccessMessage('تم نسخ النص إلى الحافظة');
    }).catch(() => {
      console.error('فشل في نسخ النص');
    });
  }
}

// تهيئة دعم اللغات
function initLanguageSupport() {
  if (!window.languageManager) {
    console.warn('مدير اللغات غير متاح');
    return;
  }

  // إعداد اختيار اللغة
  const languageSelector = document.getElementById('language-selector');
  if (languageSelector) {
    // تحديد اللغة الحالية
    languageSelector.value = languageManager.getCurrentLanguage();

    // إضافة مستمع الأحداث
    languageSelector.addEventListener('change', async (e) => {
      const newLanguage = e.target.value;
      const success = await languageManager.changeLanguage(newLanguage);

      if (success) {
        showSuccessMessage('تم تغيير اللغة بنجاح');
        // إعادة تحميل الصفحة لتطبيق التغييرات
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        showErrorMessage('فشل في تغيير اللغة');
        languageSelector.value = languageManager.getCurrentLanguage();
      }
    });
  }

  // إعداد التحكم في حجم الخط
  const fontSizeSelector = document.getElementById('font-size-selector');
  const fontPreviewText = document.getElementById('font-preview-text');

  if (fontSizeSelector && fontPreviewText) {
    fontSizeSelector.addEventListener('change', function() {
      const selectedSize = this.value;
      applyFontSize(selectedSize);
      updateFontPreview(selectedSize);

      // حفظ التفضيل
      chrome.storage.sync.set({ fontSize: selectedSize }, function() {
        showSuccessMessage('تم حفظ حجم الخط');
      });
    });

    // تحميل حجم الخط المحفوظ
    chrome.storage.sync.get(['fontSize'], function(result) {
      const savedFontSize = result.fontSize || 'medium';
      fontSizeSelector.value = savedFontSize;
      applyFontSize(savedFontSize);
      updateFontPreview(savedFontSize);
    });
  }

  // تطبيق الترجمات الحالية
  languageManager.updatePageLanguage();
}

// تهيئة نظام الملف الشخصي
function initProfileSystem() {
  const profileImageUpload = document.getElementById('profile-image-upload');
  const uploadImageBtn = document.getElementById('upload-image-btn');
  const chooseAvatarBtn = document.getElementById('choose-avatar-btn');
  const removeImageBtn = document.getElementById('remove-image-btn');
  const saveProfileBtn = document.getElementById('save-profile-btn');
  const avatarSelection = document.getElementById('avatar-selection');
  const avatarOptions = document.querySelectorAll('.avatar-option');

  // تحميل الملف الشخصي المحفوظ
  loadUserProfile();

  // رفع صورة
  if (uploadImageBtn && profileImageUpload) {
    uploadImageBtn.addEventListener('click', () => {
      profileImageUpload.click();
    });

    profileImageUpload.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.addEventListener('load', (e) => {
          const imageData = e.target.result;
          updateProfileImage(imageData, 'image');
          removeImageBtn.style.display = 'inline-block';
        });
        reader.readAsDataURL(file);
      }
    });
  }

  // اختيار أفاتار
  if (chooseAvatarBtn && avatarSelection) {
    chooseAvatarBtn.addEventListener('click', () => {
      avatarSelection.style.display = avatarSelection.style.display === 'none' ? 'block' : 'none';
    });
  }

  // خيارات الأفاتار
  avatarOptions.forEach(option => {
    option.addEventListener('click', () => {
      // إزالة التحديد السابق
      avatarOptions.forEach(opt => opt.classList.remove('selected'));
      // تحديد الخيار الحالي
      option.classList.add('selected');

      const avatar = option.dataset.avatar;
      updateProfileImage(avatar, 'avatar');
      avatarSelection.style.display = 'none';
      removeImageBtn.style.display = 'inline-block';
    });
  });

  // إزالة الصورة
  if (removeImageBtn) {
    removeImageBtn.addEventListener('click', () => {
      updateProfileImage('👤', 'avatar');
      removeImageBtn.style.display = 'none';
      avatarOptions.forEach(opt => opt.classList.remove('selected'));
    });
  }

  // حفظ الملف الشخصي
  if (saveProfileBtn) {
    saveProfileBtn.addEventListener('click', () => {
      saveUserProfile();
    });
  }
}

// تحديث الصورة الشخصية
function updateProfileImage(imageData, type) {
  const headerProfileImage = document.getElementById('header-profile-image');
  const headerProfileAvatar = document.getElementById('header-profile-avatar');
  const headerProfileInitials = document.getElementById('header-profile-initials');

  const profileImagePreview = document.getElementById('profile-image-preview');
  const profileAvatar = document.getElementById('profile-avatar');
  const profileInitials = document.getElementById('profile-initials');

  if (type === 'image') {
    // عرض الصورة
    if (headerProfileImage && profileImagePreview) {
      headerProfileImage.src = imageData;
      headerProfileImage.style.display = 'block';
      headerProfileAvatar.style.display = 'none';

      profileImagePreview.src = imageData;
      profileImagePreview.style.display = 'block';
      profileAvatar.style.display = 'none';
    }
  } else {
    // عرض الأفاتار
    if (headerProfileInitials && profileInitials) {
      headerProfileInitials.textContent = imageData;
      headerProfileImage.style.display = 'none';
      headerProfileAvatar.style.display = 'flex';

      profileInitials.textContent = imageData;
      profileImagePreview.style.display = 'none';
      profileAvatar.style.display = 'flex';
    }
  }
}

// تحميل الملف الشخصي
function loadUserProfile() {
  chrome.storage.sync.get(['userProfile'], (result) => {
    if (result.userProfile) {
      const profile = result.userProfile;

      // تحميل البريد الإلكتروني
      const userEmailInput = document.getElementById('user-email');
      if (userEmailInput && profile.email) {
        userEmailInput.value = profile.email;
      }

      // تحميل الصورة الشخصية
      if (profile.image) {
        updateProfileImage(profile.image, profile.imageType || 'avatar');

        if (profile.imageType === 'image') {
          document.getElementById('remove-image-btn').style.display = 'inline-block';
        }
      }

      // تحديث رسالة الترحيب
      if (profile.email) {
        const welcomeMessage = document.getElementById('welcome-message');
        if (welcomeMessage) {
          const name = profile.email.split('@')[0];
          welcomeMessage.textContent = `مرحباً ${name}، بارك الله فيك`;
        }
      }
    }
  });
}

// حفظ الملف الشخصي
function saveUserProfile() {
  const userEmailInput = document.getElementById('user-email');
  const headerProfileImage = document.getElementById('header-profile-image');
  const headerProfileInitials = document.getElementById('header-profile-initials');

  const profile = {
    email: userEmailInput ? userEmailInput.value : '',
    image: '',
    imageType: 'avatar'
  };

  // تحديد نوع الصورة والبيانات
  if (headerProfileImage && headerProfileImage.style.display !== 'none') {
    profile.image = headerProfileImage.src;
    profile.imageType = 'image';
  } else if (headerProfileInitials) {
    profile.image = headerProfileInitials.textContent;
    profile.imageType = 'avatar';
  }

  // حفظ البيانات
  chrome.storage.sync.set({ userProfile: profile }, () => {
    if (chrome.runtime.lastError) {
      showErrorMessage('فشل في حفظ الملف الشخصي');
    } else {
      showSuccessMessage('تم حفظ الملف الشخصي بنجاح');

      // تحديث رسالة الترحيب
      if (profile.email) {
        const welcomeMessage = document.getElementById('welcome-message');
        if (welcomeMessage) {
          const name = profile.email.split('@')[0];
          welcomeMessage.textContent = `مرحباً ${name}، بارك الله فيك`;
        }
      }
    }
  });
}

// وظائف التحكم في حجم الخط
function applyFontSize(size) {
  // إزالة جميع فئات أحجام الخط السابقة
  document.body.classList.remove('font-size-small', 'font-size-medium', 'font-size-large', 'font-size-extra-large');

  // إضافة فئة حجم الخط الجديد
  document.body.classList.add(`font-size-${size}`);
}

function updateFontPreview(size) {
  const fontPreviewText = document.getElementById('font-preview-text');
  if (fontPreviewText) {
    // إزالة جميع فئات أحجام الخط السابقة
    fontPreviewText.classList.remove('font-size-small', 'font-size-medium', 'font-size-large', 'font-size-extra-large');

    // إضافة فئة حجم الخط الجديد
    fontPreviewText.classList.add(`font-size-${size}`);
  }
}

// تهيئة تسجيل الدخول بـ Google
function initGoogleAuth() {
  if (!window.googleAuthManager) {
    console.warn('مدير Google Auth غير متاح');
    return;
  }

  // تهيئة مدير Google Auth
  window.googleAuthManager.init();

  // ربط أزرار Google Auth
  const googleSigninBtn = document.getElementById('google-signin-btn');
  const googleSignoutBtn = document.getElementById('google-signout-btn');
  const googleSyncBtn = document.getElementById('google-sync-btn');

  if (googleSigninBtn) {
    googleSigninBtn.addEventListener('click', async () => {
      googleSigninBtn.disabled = true;
      googleSigninBtn.textContent = 'جاري تسجيل الدخول...';

      await window.googleAuthManager.signIn();

      googleSigninBtn.disabled = false;
      googleSigninBtn.innerHTML = '<span class="google-icon">🔗</span> تسجيل الدخول بحساب Google';
    });
  }

  if (googleSignoutBtn) {
    googleSignoutBtn.addEventListener('click', async () => {
      await window.googleAuthManager.signOut();
    });
  }

  if (googleSyncBtn) {
    googleSyncBtn.addEventListener('click', async () => {
      googleSyncBtn.disabled = true;
      googleSyncBtn.textContent = 'جاري المزامنة...';

      await window.googleAuthManager.syncData();

      googleSyncBtn.disabled = false;
      googleSyncBtn.textContent = 'مزامنة البيانات';
    });
  }
}

// تهيئة نظام النسخ الاحتياطي
function initBackupSystem() {
  if (!window.cloudBackupManager) {
    console.warn('مدير النسخ الاحتياطي غير متاح');
    return;
  }

  // إضافة واجهة النسخ الاحتياطي
  const backupContainer = document.getElementById('backup-container');
  if (backupContainer) {
    const backupInterface = cloudBackupManager.createBackupInterface();
    backupContainer.appendChild(backupInterface);
  }

  // إنشاء نسخة احتياطية تلقائية
  cloudBackupManager.createAutoBackup();
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
  const successDiv = document.createElement('div');
  successDiv.className = 'success-message';
  successDiv.textContent = message;

  document.body.appendChild(successDiv);

  setTimeout(() => {
    if (successDiv.parentNode) {
      successDiv.parentNode.removeChild(successDiv);
    }
  }, 3000);
}

// عرض رسالة خطأ
function showErrorMessage(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'audio-error-message';
  errorDiv.textContent = message;

  document.body.appendChild(errorDiv);

  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 3000);
}
