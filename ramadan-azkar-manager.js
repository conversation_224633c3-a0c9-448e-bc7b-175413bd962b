// مدير أذكار رمضان الخاصة

class RamadanAzkarManager {
  constructor() {
    this.ramadanAzkar = {
      // أذكار الإفطار
      iftar: [
        {
          text: "اللهم لك صمت وعلى رزقك أفطرت، ذهب الظمأ وابتلت العروق وثبت الأجر إن شاء الله",
          translation: "دعاء الإفطار الأساسي",
          count: 1,
          audio: "iftar-dua.mp3",
          source: "أبو داود"
        },
        {
          text: "بسم الله، اللهم بارك لنا فيما رزقتنا وأطعمتنا واسقيتنا",
          translation: "دعاء قبل الطعام",
          count: 1,
          audio: "before-food.mp3",
          source: "الترمذي"
        },
        {
          text: "الحمد لله الذي أطعمني هذا ورزقنيه من غير حول مني ولا قوة",
          translation: "دعاء بعد الطعام",
          count: 1,
          audio: "after-food.mp3",
          source: "أبو داود"
        }
      ],
      
      // أذكار السحور
      suhoor: [
        {
          text: "اللهم بارك لنا فيما رزقتنا وزدنا منه",
          translation: "دعاء السحور",
          count: 1,
          audio: "suhoor-dua.mp3",
          source: "الترمذي"
        },
        {
          text: "نويت صيام غد عن أداء فرض رمضان هذه السنة لله تعالى",
          translation: "نية الصيام",
          count: 1,
          audio: "fasting-intention.mp3",
          source: "الفقه الإسلامي"
        },
        {
          text: "اللهم أعني على صيامي وقيامي وتلاوة كتابك",
          translation: "دعاء الاستعانة على الصيام",
          count: 1,
          audio: "fasting-help.mp3",
          source: "دعاء مأثور"
        }
      ],
      
      // أذكار ليلة القدر
      laylatAlQadr: [
        {
          text: "اللهم إنك عفو تحب العفو فاعف عني",
          translation: "دعاء ليلة القدر المأثور",
          count: 100,
          audio: "laylat-qadr-dua.mp3",
          source: "الترمذي عن عائشة رضي الله عنها"
        },
        {
          text: "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
          translation: "التهليل في ليلة القدر",
          count: 100,
          audio: "tahlil.mp3",
          source: "البخاري ومسلم"
        },
        {
          text: "سبحان الله والحمد لله ولا إله إلا الله والله أكبر",
          translation: "التسبيح الجامع",
          count: 100,
          audio: "tasbih-jamea.mp3",
          source: "الترمذي"
        }
      ],
      
      // أذكار العشر الأواخر
      lastTenNights: [
        {
          text: "اللهم أعتق رقبتي من النار",
          translation: "دعاء العتق من النار",
          count: 7,
          audio: "freedom-from-fire.mp3",
          source: "الترمذي"
        },
        {
          text: "اللهم إنك عفو كريم تحب العفو فاعف عني",
          translation: "دعاء العفو والمغفرة",
          count: 10,
          audio: "forgiveness-dua.mp3",
          source: "أحمد والترمذي"
        },
        {
          text: "رب اغفر لي ذنبي وخطئي وجهلي",
          translation: "الاستغفار الشامل",
          count: 10,
          audio: "comprehensive-istighfar.mp3",
          source: "البخاري ومسلم"
        }
      ],
      
      // أذكار التراويح
      tarawih: [
        {
          text: "سبحان ذي الملكوت والملك والكبرياء والعظمة",
          translation: "تسبيح بين السجدات",
          count: 3,
          audio: "tarawih-tasbih.mp3",
          source: "أبو داود"
        },
        {
          text: "اللهم اجعل في قلبي نوراً وفي سمعي نوراً وفي بصري نوراً",
          translation: "دعاء النور",
          count: 1,
          audio: "light-dua.mp3",
          source: "البخاري ومسلم"
        }
      ],
      
      // أذكار الاعتكاف
      itikaf: [
        {
          text: "اللهم إني أسألك من فضلك ورحمتك فإنه لا يملكها إلا أنت",
          translation: "دعاء طلب الفضل والرحمة",
          count: 3,
          audio: "fadl-rahma.mp3",
          source: "الترمذي"
        },
        {
          text: "لا حول ولا قوة إلا بالله العلي العظيم",
          translation: "الحوقلة",
          count: 100,
          audio: "hawqala.mp3",
          source: "البخاري ومسلم"
        }
      ]
    };
    
    this.ramadanSettings = {
      enabled: false,
      autoActivate: true,
      iftarReminder: true,
      suhoorReminder: true,
      iftarTime: "18:30",
      suhoorTime: "04:00",
      laylatQadrNights: [21, 23, 25, 27, 29], // الليالي الوترية
      showRamadanTheme: true
    };
    
    this.loadSettings();
  }

  // تحميل الإعدادات
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['ramadanSettings']);
      if (result.ramadanSettings) {
        this.ramadanSettings = { ...this.ramadanSettings, ...result.ramadanSettings };
      }
      
      // تحقق من شهر رمضان وتفعيل تلقائي
      if (this.ramadanSettings.autoActivate) {
        await this.checkAndActivateRamadan();
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات رمضان:', error);
    }
  }

  // حفظ الإعدادات
  async saveSettings() {
    try {
      await chrome.storage.local.set({ ramadanSettings: this.ramadanSettings });
    } catch (error) {
      console.error('خطأ في حفظ إعدادات رمضان:', error);
    }
  }

  // التحقق من شهر رمضان وتفعيل الأذكار تلقائياً
  async checkAndActivateRamadan() {
    if (!window.hijriCalendarManager) return;
    
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (hijriDate && hijriDate.month === 9) { // شهر رمضان
      this.ramadanSettings.enabled = true;
      await this.saveSettings();
      
      // تفعيل ثيم رمضان
      if (this.ramadanSettings.showRamadanTheme && window.themeManager) {
        themeManager.applyTheme('ramadan');
      }
      
      // إعداد تذكيرات رمضان
      await this.setupRamadanReminders();
      
      return true;
    } else {
      // إلغاء تفعيل أذكار رمضان خارج الشهر الكريم
      if (this.ramadanSettings.enabled) {
        this.ramadanSettings.enabled = false;
        await this.saveSettings();
      }
      return false;
    }
  }

  // إعداد تذكيرات رمضان
  async setupRamadanReminders() {
    if (!this.ramadanSettings.enabled) return;

    // إزالة التذكيرات القديمة
    await this.clearRamadanAlarms();

    const today = new Date();
    
    // تذكير الإفطار
    if (this.ramadanSettings.iftarReminder) {
      const [hours, minutes] = this.ramadanSettings.iftarTime.split(':').map(Number);
      const iftarTime = new Date(today);
      iftarTime.setHours(hours, minutes, 0, 0);
      
      if (iftarTime > today) {
        chrome.alarms.create('ramadan-iftar', {
          when: iftarTime.getTime()
        });
      }
    }
    
    // تذكير السحور
    if (this.ramadanSettings.suhoorReminder) {
      const [hours, minutes] = this.ramadanSettings.suhoorTime.split(':').map(Number);
      const suhoorTime = new Date(today);
      suhoorTime.setHours(hours, minutes, 0, 0);
      
      // إذا كان وقت السحور قد مضى اليوم، اجعله للغد
      if (suhoorTime <= today) {
        suhoorTime.setDate(suhoorTime.getDate() + 1);
      }
      
      chrome.alarms.create('ramadan-suhoor', {
        when: suhoorTime.getTime()
      });
    }
    
    // تذكيرات ليلة القدر
    await this.setupLaylatQadrReminders();
  }

  // إعداد تذكيرات ليلة القدر
  async setupLaylatQadrReminders() {
    if (!window.hijriCalendarManager) return;
    
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (!hijriDate || hijriDate.month !== 9) return;
    
    // التحقق من الليالي الوترية في العشر الأواخر
    if (this.ramadanSettings.laylatQadrNights.includes(hijriDate.day)) {
      const tonight = new Date();
      tonight.setHours(20, 0, 0, 0); // الساعة 8 مساءً
      
      if (tonight > new Date()) {
        chrome.alarms.create('laylat-qadr-reminder', {
          when: tonight.getTime()
        });
      }
    }
  }

  // إزالة تذكيرات رمضان
  async clearRamadanAlarms() {
    const alarms = await chrome.alarms.getAll();
    for (const alarm of alarms) {
      if (alarm.name.startsWith('ramadan-') || alarm.name.startsWith('laylat-qadr-')) {
        chrome.alarms.clear(alarm.name);
      }
    }
  }

  // الحصول على أذكار حسب النوع
  getAzkarByType(type) {
    return this.ramadanAzkar[type] || [];
  }

  // الحصول على أذكار اليوم حسب التاريخ الهجري
  getTodaySpecialAzkar() {
    if (!this.ramadanSettings.enabled) return [];
    
    if (!window.hijriCalendarManager) return [];
    
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (!hijriDate || hijriDate.month !== 9) return [];
    
    const specialAzkar = [];
    
    // ليلة القدر
    if (this.ramadanSettings.laylatQadrNights.includes(hijriDate.day)) {
      specialAzkar.push({
        title: 'أذكار ليلة القدر',
        type: 'laylatAlQadr',
        azkar: this.ramadanAzkar.laylatAlQadr,
        importance: 'high'
      });
    }
    
    // العشر الأواخر
    if (hijriDate.day >= 21) {
      specialAzkar.push({
        title: 'أذكار العشر الأواخر',
        type: 'lastTenNights',
        azkar: this.ramadanAzkar.lastTenNights,
        importance: 'high'
      });
    }
    
    // أذكار عامة لرمضان
    specialAzkar.push({
      title: 'أذكار الإفطار',
      type: 'iftar',
      azkar: this.ramadanAzkar.iftar,
      importance: 'medium'
    });
    
    specialAzkar.push({
      title: 'أذكار السحور',
      type: 'suhoor',
      azkar: this.ramadanAzkar.suhoor,
      importance: 'medium'
    });
    
    return specialAzkar;
  }

  // تحديث أوقات الإفطار والسحور
  updateRamadanTimes(iftarTime, suhoorTime) {
    this.ramadanSettings.iftarTime = iftarTime;
    this.ramadanSettings.suhoorTime = suhoorTime;
    this.saveSettings();
    
    // إعادة إعداد التذكيرات
    this.setupRamadanReminders();
  }

  // تفعيل/إلغاء أذكار رمضان يدوياً
  toggleRamadanMode(enabled) {
    this.ramadanSettings.enabled = enabled;
    this.saveSettings();
    
    if (enabled) {
      this.setupRamadanReminders();
      
      // تفعيل ثيم رمضان
      if (this.ramadanSettings.showRamadanTheme && window.themeManager) {
        themeManager.applyTheme('ramadan');
      }
    } else {
      this.clearRamadanAlarms();
    }
  }

  // الحصول على معلومات رمضان الحالية
  getRamadanInfo() {
    if (!window.hijriCalendarManager) return null;
    
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (!hijriDate || hijriDate.month !== 9) return null;
    
    const monthLength = hijriCalendarManager.getHijriMonthLength(hijriDate.year, 9);
    const daysRemaining = monthLength - hijriDate.day;
    const progress = (hijriDate.day / monthLength) * 100;
    
    return {
      currentDay: hijriDate.day,
      totalDays: monthLength,
      daysRemaining,
      progress,
      isLastTenNights: hijriDate.day >= 21,
      isPossibleLaylatQadr: this.ramadanSettings.laylatQadrNights.includes(hijriDate.day)
    };
  }

  // الحصول على نصائح رمضانية لليوم
  getTodayRamadanTips() {
    const ramadanInfo = this.getRamadanInfo();
    if (!ramadanInfo) return [];
    
    const tips = [];
    
    if (ramadanInfo.isPossibleLaylatQadr) {
      tips.push('🌙 هذه ليلة وترية محتملة لليلة القدر - أكثر من الدعاء والذكر');
      tips.push('📿 ادع بدعاء ليلة القدر: "اللهم إنك عفو تحب العفو فاعف عني"');
    }
    
    if (ramadanInfo.isLastTenNights) {
      tips.push('✨ نحن في العشر الأواخر - وقت الاعتكاف والتهجد');
      tips.push('🤲 أكثر من الاستغفار والدعاء');
    }
    
    if (ramadanInfo.daysRemaining <= 3) {
      tips.push('⏰ أيام قليلة متبقية من رمضان - اغتنم كل لحظة');
      tips.push('💰 لا تنس زكاة الفطر');
    }
    
    // نصائح عامة
    tips.push('📖 اقرأ جزءاً من القرآن يومياً');
    tips.push('🕌 صل التراويح في المسجد إن أمكن');
    
    return tips;
  }

  // إحصائيات رمضان
  async getRamadanStats() {
    try {
      const result = await chrome.storage.local.get(['ramadanStats']);
      const stats = result.ramadanStats || {
        daysCompleted: 0,
        iftarAzkarRead: 0,
        suhoorAzkarRead: 0,
        laylatQadrAzkarRead: 0,
        totalAzkarRead: 0
      };
      
      return stats;
    } catch (error) {
      console.error('خطأ في جلب إحصائيات رمضان:', error);
      return null;
    }
  }

  // تحديث إحصائيات رمضان
  async updateRamadanStats(type) {
    try {
      const result = await chrome.storage.local.get(['ramadanStats']);
      const stats = result.ramadanStats || {
        daysCompleted: 0,
        iftarAzkarRead: 0,
        suhoorAzkarRead: 0,
        laylatQadrAzkarRead: 0,
        totalAzkarRead: 0
      };
      
      switch (type) {
        case 'iftar':
          stats.iftarAzkarRead++;
          break;
        case 'suhoor':
          stats.suhoorAzkarRead++;
          break;
        case 'laylatQadr':
          stats.laylatQadrAzkarRead++;
          break;
      }
      
      stats.totalAzkarRead++;
      
      await chrome.storage.local.set({ ramadanStats: stats });
    } catch (error) {
      console.error('خطأ في تحديث إحصائيات رمضان:', error);
    }
  }
}

// إنشاء مثيل عام لمدير أذكار رمضان
const ramadanAzkarManager = new RamadanAzkarManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.ramadanAzkarManager = ramadanAzkarManager;
}
