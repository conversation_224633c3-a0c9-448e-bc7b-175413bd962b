// مدير مشاركة الأذكار

class ShareManager {
  constructor() {
    this.canvasWidth = 800;
    this.canvasHeight = 600;
    this.templates = {
      classic: {
        name: 'كلاسيكي',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: '#ffffff',
        accentColor: '#ffd700'
      },
      islamic: {
        name: 'إسلامي',
        background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
        textColor: '#ffffff',
        accentColor: '#81C784'
      },
      elegant: {
        name: 'أنيق',
        background: 'linear-gradient(135deg, #434343 0%, #000000 100%)',
        textColor: '#ffffff',
        accentColor: '#FFD700'
      },
      ramadan: {
        name: 'رمضان',
        background: 'linear-gradient(135deg, #8E24AA 0%, #FFD700 100%)',
        textColor: '#ffffff',
        accentColor: '#E1BEE7'
      }
    };
  }

  // إنشاء صورة للذكر
  async createZikrImage(zikrText, translation = '', template = 'classic', options = {}) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = this.canvasWidth;
    canvas.height = this.canvasHeight;
    
    const templateStyle = this.templates[template] || this.templates.classic;
    
    // رسم الخلفية
    await this.drawBackground(ctx, templateStyle.background);
    
    // رسم الزخارف الإسلامية
    this.drawIslamicDecorations(ctx, templateStyle.accentColor);
    
    // رسم النص العربي
    this.drawArabicText(ctx, zikrText, templateStyle.textColor, options);
    
    // رسم الترجمة إذا وجدت
    if (translation) {
      this.drawTranslation(ctx, translation, templateStyle.accentColor);
    }
    
    // رسم التوقيع
    this.drawSignature(ctx, templateStyle.accentColor);
    
    return canvas.toDataURL('image/png');
  }

  // رسم الخلفية
  async drawBackground(ctx, background) {
    if (background.startsWith('linear-gradient')) {
      // استخراج الألوان من التدرج
      const colors = background.match(/#[0-9a-fA-F]{6}/g);
      if (colors && colors.length >= 2) {
        const gradient = ctx.createLinearGradient(0, 0, this.canvasWidth, this.canvasHeight);
        gradient.addColorStop(0, colors[0]);
        gradient.addColorStop(1, colors[1]);
        ctx.fillStyle = gradient;
      }
    } else {
      ctx.fillStyle = background;
    }
    
    ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
    
    // إضافة تأثير شفافية للنمط
    ctx.globalAlpha = 0.1;
    this.drawPattern(ctx);
    ctx.globalAlpha = 1.0;
  }

  // رسم نمط الخلفية
  drawPattern(ctx) {
    const patternSize = 50;
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 1;
    
    for (let x = 0; x < this.canvasWidth; x += patternSize) {
      for (let y = 0; y < this.canvasHeight; y += patternSize) {
        // رسم نجمة إسلامية بسيطة
        this.drawStar(ctx, x + patternSize/2, y + patternSize/2, 8, 3, 5);
      }
    }
  }

  // رسم نجمة
  drawStar(ctx, cx, cy, spikes, outerRadius, innerRadius) {
    let rot = Math.PI / 2 * 3;
    let x = cx;
    let y = cy;
    const step = Math.PI / spikes;

    ctx.beginPath();
    ctx.moveTo(cx, cy - outerRadius);
    
    for (let i = 0; i < spikes; i++) {
      x = cx + Math.cos(rot) * outerRadius;
      y = cy + Math.sin(rot) * outerRadius;
      ctx.lineTo(x, y);
      rot += step;

      x = cx + Math.cos(rot) * innerRadius;
      y = cy + Math.sin(rot) * innerRadius;
      ctx.lineTo(x, y);
      rot += step;
    }
    
    ctx.lineTo(cx, cy - outerRadius);
    ctx.closePath();
    ctx.stroke();
  }

  // رسم الزخارف الإسلامية
  drawIslamicDecorations(ctx, color) {
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.globalAlpha = 0.3;
    
    // رسم إطار زخرفي
    const margin = 40;
    const cornerSize = 60;
    
    // الزوايا العلوية
    this.drawCornerDecoration(ctx, margin, margin, cornerSize, 0);
    this.drawCornerDecoration(ctx, this.canvasWidth - margin, margin, cornerSize, 90);
    
    // الزوايا السفلية
    this.drawCornerDecoration(ctx, margin, this.canvasHeight - margin, cornerSize, 270);
    this.drawCornerDecoration(ctx, this.canvasWidth - margin, this.canvasHeight - margin, cornerSize, 180);
    
    ctx.globalAlpha = 1.0;
  }

  // رسم زخرفة الزاوية
  drawCornerDecoration(ctx, x, y, size, rotation) {
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(rotation * Math.PI / 180);
    
    ctx.beginPath();
    ctx.arc(0, 0, size/2, 0, Math.PI/2);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.arc(0, 0, size/3, 0, Math.PI/2);
    ctx.stroke();
    
    ctx.restore();
  }

  // رسم النص العربي
  drawArabicText(ctx, text, color, options = {}) {
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // تحديد حجم الخط حسب طول النص
    let fontSize = this.calculateFontSize(text, this.canvasWidth - 160);
    ctx.font = `${fontSize}px 'Amiri', 'Traditional Arabic', serif`;
    
    // تقسيم النص إلى أسطر
    const lines = this.wrapText(ctx, text, this.canvasWidth - 160);
    const lineHeight = fontSize * 1.4;
    const totalHeight = lines.length * lineHeight;
    const startY = (this.canvasHeight - totalHeight) / 2;
    
    // رسم كل سطر
    lines.forEach((line, index) => {
      const y = startY + (index * lineHeight) + (lineHeight / 2);
      
      // إضافة ظل للنص
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;
      
      ctx.fillText(line, this.canvasWidth / 2, y);
      
      // إزالة الظل
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    });
  }

  // حساب حجم الخط المناسب
  calculateFontSize(text, maxWidth) {
    const baseSize = 48;
    const textLength = text.length;
    
    if (textLength < 50) return baseSize;
    if (textLength < 100) return baseSize - 8;
    if (textLength < 150) return baseSize - 16;
    return baseSize - 24;
  }

  // تقسيم النص إلى أسطر
  wrapText(ctx, text, maxWidth) {
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + words[i] + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        lines.push(currentLine.trim());
        currentLine = words[i] + ' ';
      } else {
        currentLine = testLine;
      }
    }
    
    if (currentLine.trim()) {
      lines.push(currentLine.trim());
    }
    
    return lines;
  }

  // رسم الترجمة
  drawTranslation(ctx, translation, color) {
    ctx.fillStyle = color;
    ctx.font = '24px Arial, sans-serif';
    ctx.textAlign = 'center';
    
    const y = this.canvasHeight - 120;
    
    // إضافة ظل خفيف
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;
    
    ctx.fillText(translation, this.canvasWidth / 2, y);
    
    // إزالة الظل
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  }

  // رسم التوقيع
  drawSignature(ctx, color) {
    ctx.fillStyle = color;
    ctx.font = '18px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.globalAlpha = 0.7;
    
    const signature = 'أذكار المسلم - Muslim Azkar';
    ctx.fillText(signature, this.canvasWidth / 2, this.canvasHeight - 40);
    
    ctx.globalAlpha = 1.0;
  }

  // مشاركة النص فقط
  async shareText(zikrText, translation = '') {
    const shareData = {
      title: 'ذكر من أذكار المسلم',
      text: `${zikrText}\n\n${translation ? translation + '\n\n' : ''}من تطبيق أذكار المسلم`,
      url: window.location.href
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
        return { success: true, method: 'native' };
      } else {
        // نسخ إلى الحافظة كبديل
        await navigator.clipboard.writeText(shareData.text);
        this.showSuccessMessage('تم نسخ النص إلى الحافظة');
        return { success: true, method: 'clipboard' };
      }
    } catch (error) {
      console.error('خطأ في المشاركة:', error);
      return { success: false, error: error.message };
    }
  }

  // مشاركة الصورة
  async shareImage(imageDataUrl, zikrText) {
    try {
      // تحويل data URL إلى blob
      const response = await fetch(imageDataUrl);
      const blob = await response.blob();
      
      const file = new File([blob], 'zikr.png', { type: 'image/png' });
      
      const shareData = {
        title: 'ذكر من أذكار المسلم',
        text: zikrText,
        files: [file]
      };

      if (navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        return { success: true, method: 'native' };
      } else {
        // تحميل الصورة كبديل
        this.downloadImage(imageDataUrl, 'zikr.png');
        return { success: true, method: 'download' };
      }
    } catch (error) {
      console.error('خطأ في مشاركة الصورة:', error);
      return { success: false, error: error.message };
    }
  }

  // تحميل الصورة
  downloadImage(dataUrl, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    this.showSuccessMessage('تم تحميل الصورة بنجاح');
  }

  // نسخ الصورة إلى الحافظة
  async copyImageToClipboard(imageDataUrl) {
    try {
      const response = await fetch(imageDataUrl);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      
      this.showSuccessMessage('تم نسخ الصورة إلى الحافظة');
      return true;
    } catch (error) {
      console.error('خطأ في نسخ الصورة:', error);
      return false;
    }
  }

  // عرض رسالة نجاح
  showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      if (successDiv.parentNode) {
        successDiv.parentNode.removeChild(successDiv);
      }
    }, 3000);
  }

  // إنشاء واجهة مشاركة
  createShareDialog(zikrText, translation = '') {
    const dialog = document.createElement('div');
    dialog.className = 'share-dialog';
    dialog.innerHTML = `
      <div class="share-dialog-content">
        <div class="share-dialog-header">
          <h3>مشاركة الذكر</h3>
          <button class="close-dialog">×</button>
        </div>
        
        <div class="share-preview">
          <div class="zikr-preview">
            <p class="zikr-text">${zikrText}</p>
            ${translation ? `<p class="zikr-translation">${translation}</p>` : ''}
          </div>
        </div>
        
        <div class="share-options">
          <h4>اختر طريقة المشاركة:</h4>
          
          <div class="share-buttons">
            <button class="share-btn text-share" data-type="text">
              📝 مشاركة النص
            </button>
            <button class="share-btn image-share" data-type="image">
              🖼️ إنشاء صورة
            </button>
            <button class="share-btn copy-share" data-type="copy">
              📋 نسخ النص
            </button>
          </div>
          
          <div class="template-selection" style="display: none;">
            <h4>اختر تصميم الصورة:</h4>
            <div class="template-buttons">
              ${Object.entries(this.templates).map(([key, template]) => 
                `<button class="template-btn" data-template="${key}">${template.name}</button>`
              ).join('')}
            </div>
          </div>
          
          <div class="image-preview" style="display: none;">
            <canvas class="preview-canvas"></canvas>
            <div class="image-actions">
              <button class="download-image">تحميل الصورة</button>
              <button class="copy-image">نسخ الصورة</button>
              <button class="share-image">مشاركة الصورة</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // إضافة مستمعي الأحداث
    this.attachShareDialogEvents(dialog, zikrText, translation);
    
    return dialog;
  }

  // ربط أحداث حوار المشاركة
  attachShareDialogEvents(dialog, zikrText, translation) {
    const closeBtn = dialog.querySelector('.close-dialog');
    const shareButtons = dialog.querySelectorAll('.share-btn');
    const templateButtons = dialog.querySelectorAll('.template-btn');
    const templateSelection = dialog.querySelector('.template-selection');
    const imagePreview = dialog.querySelector('.image-preview');
    
    // إغلاق الحوار
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(dialog);
    });
    
    // أزرار المشاركة
    shareButtons.forEach(btn => {
      btn.addEventListener('click', async () => {
        const type = btn.dataset.type;
        
        switch (type) {
          case 'text':
            await this.shareText(zikrText, translation);
            break;
          case 'copy':
            await navigator.clipboard.writeText(`${zikrText}\n\n${translation || ''}`);
            this.showSuccessMessage('تم نسخ النص إلى الحافظة');
            break;
          case 'image':
            templateSelection.style.display = 'block';
            break;
        }
      });
    });
    
    // أزرار القوالب
    templateButtons.forEach(btn => {
      btn.addEventListener('click', async () => {
        const template = btn.dataset.template;
        const imageDataUrl = await this.createZikrImage(zikrText, translation, template);
        
        const canvas = dialog.querySelector('.preview-canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.addEventListener('load', () => {
          canvas.width = 400;
          canvas.height = 300;
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        });
        
        img.src = imageDataUrl;
        imagePreview.style.display = 'block';
        
        // ربط أحداث الصورة
        this.attachImageActions(dialog, imageDataUrl, zikrText);
      });
    });
  }

  // ربط أحداث الصورة
  attachImageActions(dialog, imageDataUrl, zikrText) {
    const downloadBtn = dialog.querySelector('.download-image');
    const copyBtn = dialog.querySelector('.copy-image');
    const shareBtn = dialog.querySelector('.share-image');
    
    downloadBtn.addEventListener('click', () => this.downloadImage(imageDataUrl, 'zikr.png'));
    copyBtn.addEventListener('click', () => this.copyImageToClipboard(imageDataUrl));
    shareBtn.addEventListener('click', () => this.shareImage(imageDataUrl, zikrText));
  }

  // عرض حوار المشاركة
  showShareDialog(zikrText, translation = '') {
    const dialog = this.createShareDialog(zikrText, translation);
    document.body.appendChild(dialog);
  }
}

// إنشاء مثيل عام لمدير المشاركة
const shareManager = new ShareManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.shareManager = shareManager;
}
