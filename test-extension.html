<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة أذكار المسلم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #4CAF50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-right: 3px solid #4CAF50;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .warning {
            color: #ff9800;
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🕌 اختبار إضافة أذكار المسلم</h1>
        <p>استخدم هذه الصفحة للتأكد من أن إضافة Chrome تعمل بشكل صحيح</p>

        <div class="test-section">
            <div class="test-title">1. اختبار تثبيت الإضافة</div>
            <div class="test-step">
                ✅ تحقق من ظهور أيقونة الإضافة في شريط الأدوات
            </div>
            <div class="test-step">
                ✅ انقر على الأيقونة للتأكد من فتح النافذة المنبثقة
            </div>
            <div class="test-step">
                ✅ تحقق من ظهور جميع التبويبات (الرئيسية، أذكار الصباح، المساء، بعد الصلاة، الإعدادات)
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. اختبار عرض الأذكار</div>
            <div class="test-step">
                📖 انقر على تبويب "أذكار الصباح" وتحقق من ظهور الأذكار
            </div>
            <div class="test-step">
                🌙 انقر على تبويب "أذكار المساء" وتحقق من ظهور الأذكار
            </div>
            <div class="test-step">
                🤲 انقر على تبويب "أذكار بعد الصلاة" وتحقق من ظهور الأذكار
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. اختبار العداد التفاعلي</div>
            <div class="test-step">
                ➕ جرب النقر على زر "+" في أي ذكر يحتوي على عداد
            </div>
            <div class="test-step">
                🔄 جرب زر "إعادة تعيين" للتأكد من عمله
            </div>
            <div class="test-step">
                ✅ تحقق من تغير لون العداد عند إكمال العدد المطلوب
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. اختبار الإعدادات</div>
            <div class="test-step">
                ⚙️ اذهب إلى تبويب "الإعدادات"
            </div>
            <div class="test-step">
                🕐 غيّر وقت أذكار الصباح والمساء
            </div>
            <div class="test-step">
                💾 انقر "حفظ الإعدادات" وتحقق من ظهور رسالة النجاح
            </div>
            <div class="test-step">
                🔄 أعد فتح الإضافة وتحقق من حفظ الإعدادات
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. اختبار الإشعارات</div>
            <div class="test-step">
                🔔 تأكد من السماح للمتصفح بإرسال الإشعارات
            </div>
            <div class="test-step">
                ⏰ اضبط وقت الأذكار لوقت قريب (خلال دقيقة) لاختبار الإشعارات
            </div>
            <div class="test-step">
                📱 انتظر ظهور الإشعار في الوقت المحدد
            </div>
            <div class="test-step">
                👆 جرب النقر على الإشعار للتأكد من فتح الإضافة
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">6. اختبار الأخطاء الشائعة</div>
            <div class="test-step">
                🔍 افتح أدوات المطور (F12) وتحقق من عدم وجود أخطاء في وحدة التحكم
            </div>
            <div class="test-step">
                📁 تحقق من وجود جميع الملفات المطلوبة في مجلد الإضافة
            </div>
            <div class="test-step">
                🖼️ تحقق من وجود الأيقونات في مجلد images/
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">نتائج الاختبار</div>
            <div id="test-results">
                <p>قم بتنفيذ الاختبارات أعلاه وسجل النتائج هنا:</p>
                <button id="mark-success-btn">✅ جميع الاختبارات نجحت</button>
                <button id="mark-partial-btn">⚠️ بعض الاختبارات فشلت</button>
                <button id="mark-failed-btn">❌ معظم الاختبارات فشلت</button>
                <div id="result-message"></div>
            </div>
        </div>
    </div>

    <script>
        // إضافة event listeners عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const markSuccessBtn = document.getElementById('mark-success-btn');
            const markPartialBtn = document.getElementById('mark-partial-btn');
            const markFailedBtn = document.getElementById('mark-failed-btn');

            if (markSuccessBtn) {
                markSuccessBtn.addEventListener('click', markSuccess);
            }

            if (markPartialBtn) {
                markPartialBtn.addEventListener('click', markPartial);
            }

            if (markFailedBtn) {
                markFailedBtn.addEventListener('click', markFailed);
            }
        });

        function markSuccess() {
            document.getElementById('result-message').innerHTML =
                '<div class="success">🎉 ممتاز! الإضافة تعمل بشكل مثالي. يمكنك الآن استخدامها بثقة.</div>';
        }

        function markPartial() {
            document.getElementById('result-message').innerHTML =
                '<div class="warning">⚠️ هناك بعض المشاكل. راجع الملفات والإعدادات وأعد المحاولة.</div>';
        }

        function markFailed() {
            document.getElementById('result-message').innerHTML =
                '<div class="error">❌ هناك مشاكل كبيرة. راجع تعليمات التثبيت وتأكد من وجود جميع الملفات.</div>';
        }
    </script>
</body>
</html>
