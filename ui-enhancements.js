// تحسينات واجهة المستخدم والتجربة
class UIEnhancements {
  constructor() {
    this.isInitialized = false;
    this.animations = new Map();
    this.themes = {
      light: {
        primary: '#4CAF50',
        secondary: '#45a049',
        background: '#ffffff',
        text: '#333333',
        accent: '#FF6B6B'
      },
      dark: {
        primary: '#66BB6A',
        secondary: '#4CAF50',
        background: '#1a1a1a',
        text: '#ffffff',
        accent: '#FF8A80'
      },
      islamic: {
        primary: '#2E7D32',
        secondary: '#1B5E20',
        background: '#F1F8E9',
        text: '#1B5E20',
        accent: '#FF6F00'
      }
    };
  }

  // تهيئة التحسينات
  async init() {
    if (this.isInitialized) return;
    
    try {
      await this.loadUserPreferences();
      this.setupSmoothTransitions();
      this.enhanceArabicFonts();
      this.addLoadingStates();
      this.setupResponsiveDesign();
      this.addAccessibilityFeatures();
      this.setupPerformanceOptimizations();
      
      this.isInitialized = true;
      console.log('✅ تم تهيئة تحسينات واجهة المستخدم');
    } catch (error) {
      console.error('❌ خطأ في تهيئة تحسينات واجهة المستخدم:', error);
    }
  }

  // تحميل تفضيلات المستخدم
  async loadUserPreferences() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['uiPreferences'], (result) => {
        this.preferences = result.uiPreferences || {
          theme: 'light',
          fontSize: 'medium',
          animations: true,
          reducedMotion: false
        };
        resolve();
      });
    });
  }

  // إعداد الانتقالات السلسة
  setupSmoothTransitions() {
    const style = document.createElement('style');
    style.textContent = `
      /* انتقالات سلسة عامة */
      * {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* انتقالات خاصة للأزرار */
      .btn, button {
        transition: all 0.2s ease;
        transform: translateZ(0);
      }

      .btn:hover, button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .btn:active, button:active {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      /* انتقالات للبطاقات */
      .zikr-card, .card {
        transition: all 0.3s ease;
        transform: translateZ(0);
      }

      .zikr-card:hover, .card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      /* انتقالات للتبويبات */
      .tab-button {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
      }

      .tab-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      .tab-button:hover::before {
        left: 100%;
      }

      /* انتقالات للنصوص */
      .fade-in {
        animation: fadeIn 0.5s ease-in;
      }

      .slide-in {
        animation: slideIn 0.4s ease-out;
      }

      .scale-in {
        animation: scaleIn 0.3s ease-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideIn {
        from { 
          opacity: 0;
          transform: translateY(20px);
        }
        to { 
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes scaleIn {
        from { 
          opacity: 0;
          transform: scale(0.9);
        }
        to { 
          opacity: 1;
          transform: scale(1);
        }
      }

      /* تحسينات للحركة المخفضة */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `;
    
    document.head.appendChild(style);
  }

  // تحسين الخطوط العربية
  enhanceArabicFonts() {
    const arabicFontStyle = document.createElement('style');
    arabicFontStyle.textContent = `
      /* خطوط عربية محسنة */
      body, .arabic-text {
        font-family: 'Amiri', 'Noto Sans Arabic', 'Cairo', 'Tajawal', 'IBM Plex Sans Arabic', sans-serif;
        font-feature-settings: 'liga' 1, 'calt' 1, 'kern' 1;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* تحسين قراءة النصوص العربية */
      .zikr-text, .arabic-content {
        line-height: 1.8;
        letter-spacing: 0.02em;
        word-spacing: 0.1em;
        direction: rtl;
        text-align: right;
      }

      /* أحجام خطوط متدرجة */
      .font-small { font-size: 14px; }
      .font-medium { font-size: 16px; }
      .font-large { font-size: 18px; }
      .font-xlarge { font-size: 20px; }

      /* تحسين عرض الآيات والأذكار */
      .verse-text {
        font-size: 18px;
        line-height: 2;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        border-right: 4px solid #4CAF50;
        margin: 10px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* تحسين عرض التشكيل */
      .tashkeel {
        font-feature-settings: 'dlig' 1, 'liga' 1, 'calt' 1;
      }
    `;
    
    document.head.appendChild(arabicFontStyle);
    
    // تطبيق حجم الخط المفضل
    this.applyFontSize(this.preferences.fontSize);
  }

  // تطبيق حجم الخط
  applyFontSize(size) {
    const sizeClasses = {
      small: 'font-small',
      medium: 'font-medium',
      large: 'font-large',
      xlarge: 'font-xlarge'
    };
    
    // إزالة الأحجام السابقة
    Object.values(sizeClasses).forEach(className => {
      document.body.classList.remove(className);
    });
    
    // إضافة الحجم الجديد
    if (sizeClasses[size]) {
      document.body.classList.add(sizeClasses[size]);
    }
  }

  // إضافة حالات التحميل
  addLoadingStates() {
    const loadingStyle = document.createElement('style');
    loadingStyle.textContent = `
      /* حالات التحميل */
      .loading {
        position: relative;
        pointer-events: none;
        opacity: 0.7;
      }

      .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #4CAF50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1000;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* skeleton loading للبطاقات */
      .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }

      .skeleton-text {
        height: 16px;
        margin: 8px 0;
        border-radius: 4px;
      }

      .skeleton-title {
        height: 20px;
        width: 60%;
        margin: 12px 0;
        border-radius: 4px;
      }
    `;
    
    document.head.appendChild(loadingStyle);
  }

  // إعداد التصميم المتجاوب
  setupResponsiveDesign() {
    const responsiveStyle = document.createElement('style');
    responsiveStyle.textContent = `
      /* تصميم متجاوب محسن */
      @media (max-width: 480px) {
        .container {
          padding: 10px;
          margin: 5px;
        }
        
        .zikr-card {
          margin: 8px 0;
          padding: 12px;
        }
        
        .btn {
          padding: 8px 16px;
          font-size: 14px;
        }
        
        .tab-button {
          padding: 8px 12px;
          font-size: 13px;
        }
        
        .points-display {
          padding: 15px;
          margin: 8px 0;
        }
      }

      @media (max-width: 360px) {
        .container {
          padding: 8px;
          margin: 3px;
        }
        
        .zikr-text {
          font-size: 15px;
          line-height: 1.6;
        }
        
        .btn {
          padding: 6px 12px;
          font-size: 13px;
        }
      }

      @media (min-width: 768px) {
        .container {
          max-width: 600px;
          margin: 0 auto;
        }
        
        .zikr-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;
        }
      }

      /* تحسينات للشاشات الكبيرة */
      @media (min-width: 1024px) {
        .container {
          max-width: 800px;
        }
        
        .zikr-grid {
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
        }
      }
    `;
    
    document.head.appendChild(responsiveStyle);
  }

  // إضافة ميزات إمكانية الوصول
  addAccessibilityFeatures() {
    // إضافة ARIA labels
    document.querySelectorAll('button').forEach(button => {
      if (!button.getAttribute('aria-label') && button.textContent) {
        button.setAttribute('aria-label', button.textContent.trim());
      }
    });

    // إضافة focus indicators محسنة
    const accessibilityStyle = document.createElement('style');
    accessibilityStyle.textContent = `
      /* تحسينات إمكانية الوصول */
      *:focus {
        outline: 2px solid #4CAF50;
        outline-offset: 2px;
        border-radius: 4px;
      }

      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }

      /* تحسين التباين للنصوص */
      .high-contrast {
        filter: contrast(1.2);
      }

      /* تحسين حجم منطقة اللمس */
      button, .btn, .clickable {
        min-height: 44px;
        min-width: 44px;
      }
    `;
    
    document.head.appendChild(accessibilityStyle);

    // إضافة دعم لوحة المفاتيح
    this.setupKeyboardNavigation();
  }

  // إعداد التنقل بلوحة المفاتيح
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // التنقل بين التبويبات بالأسهم
      if (e.target.classList.contains('tab-button')) {
        const tabs = Array.from(document.querySelectorAll('.tab-button'));
        const currentIndex = tabs.indexOf(e.target);
        
        if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
          e.preventDefault();
          const nextIndex = e.key === 'ArrowRight' 
            ? (currentIndex + 1) % tabs.length
            : (currentIndex - 1 + tabs.length) % tabs.length;
          
          tabs[nextIndex].focus();
          tabs[nextIndex].click();
        }
      }
      
      // اختصارات لوحة المفاتيح
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
          case '2':
          case '3':
          case '4':
          case '5':
            e.preventDefault();
            const tabIndex = parseInt(e.key) - 1;
            const tab = document.querySelectorAll('.tab-button')[tabIndex];
            if (tab) {
              tab.focus();
              tab.click();
            }
            break;
        }
      }
    });
  }

  // تحسينات الأداء
  setupPerformanceOptimizations() {
    // Lazy loading للصور
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }

    // تحسين الأداء للأحداث
    this.debounce = (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    };

    // تحسين scroll events
    let ticking = false;
    const updateScrollPosition = () => {
      // تحديث موقع التمرير
      ticking = false;
    };

    document.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollPosition);
        ticking = true;
      }
    });
  }

  // تطبيق ثيم
  applyTheme(themeName) {
    const theme = this.themes[themeName];
    if (!theme) return;

    const root = document.documentElement;
    Object.entries(theme).forEach(([property, value]) => {
      root.style.setProperty(`--color-${property}`, value);
    });

    // حفظ التفضيل
    this.preferences.theme = themeName;
    this.savePreferences();
  }

  // حفظ التفضيلات
  async savePreferences() {
    return new Promise((resolve) => {
      chrome.storage.sync.set({ uiPreferences: this.preferences }, resolve);
    });
  }

  // إضافة تأثير تحميل للعنصر
  showLoading(element) {
    if (element) {
      element.classList.add('loading');
    }
  }

  // إزالة تأثير التحميل
  hideLoading(element) {
    if (element) {
      element.classList.remove('loading');
    }
  }

  // إضافة تأثير fade in للعنصر
  fadeIn(element, delay = 0) {
    if (element) {
      setTimeout(() => {
        element.classList.add('fade-in');
      }, delay);
    }
  }

  // إضافة تأثير slide in للعنصر
  slideIn(element, delay = 0) {
    if (element) {
      setTimeout(() => {
        element.classList.add('slide-in');
      }, delay);
    }
  }

  // إضافة تأثير scale in للعنصر
  scaleIn(element, delay = 0) {
    if (element) {
      setTimeout(() => {
        element.classList.add('scale-in');
      }, delay);
    }
  }
}

// إنشاء مثيل عام
window.uiEnhancements = new UIEnhancements();
